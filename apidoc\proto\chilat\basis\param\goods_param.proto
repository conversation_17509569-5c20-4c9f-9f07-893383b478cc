syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";
import "chilat/marketing/marketing_common.proto";
import "common/business.proto";


message MidGoodsPageQueryParamByRule {
  common.PageParam page = 1;
  MidGoodsQueryParamByRuleParam query = 2;
}

message MidGoodsQueryParamByRuleParam {
  string marketingRuleId = 1; //规则id
  repeated string categoryIds = 4; //分类
  repeated string brandIds = 5; //品牌
  int32 minimumSales = 6; //最低销量
  int32 quantity = 7; //展示数量
  chilat.marketing.GoodsOnlineDays goodsOnlineDays = 8; //商品上架时间
  repeated string goodsIds = 9; //商品id
  string keyword = 10; //关键字
  bool isMall = 11; //是否是商城
  chilat.marketing.StockLocation stockLocation = 12; //库存位置
}

message MidGoodsInfoQueryParam {
  string id = 10; //根据接口，传 goodsId 或 skuId
  string code = 15; //根据接口，传 goodsNo 或 skuNo
  common.CurrencyType responseCurrency = 20; //响应结果中的货币单位（必填）
  bool previewMode = 30; //是否为预览模式（预览模式下，若商品未下架状态，则返回所有下架SKU；默认false）
  int32 extraQueryFlag = 40; //额外查询标志（额外查询非上架状态的商品的标志）
  int32 siteId = 50; //站点ID
  string visitorId = 60; //商城访客ID
  string userId = 70; //商城用户ID
  string padc = 88; //促销活动动态代码
}

message MidSearchGoodsParam {
  repeated string goodsIds = 5; //商品ID列表
  repeated string frontendCategoryIds = 10; //商城前台类目ID（营销分类；营销分类、营销规则ID、营销规则代码等三者取并集）
  repeated string frontendChildCategoryIds = 11; //商城前台子类目ID（若此值非空，则以子类目ID取商品数据，父类目ID取过滤项）
  repeated string backendCategoryIds = 15; //admin后台类目ID（admin商品分类）
  repeated string marketingRuleIds = 21; //营销规则ID（营销分类、营销规则ID、营销规则代码等三者取并集）
  repeated string marketingRuleCodes = 22; //营销规则代码（营销分类、营销规则ID、营销规则代码等三者取并集）
  repeated string brandIds = 25; //品牌ID，多个用下划线（_）分隔
  string keyword = 30; //关键字
  double minPrice = 45; //最低价限制
  double maxPrice = 60; //最高价限制
  int64 minPublishTime = 61; //最早发布时间（包含；毫秒时间戳，GTM+8时区）
  int64 maxPublishTime = 62; //最晚发布时间（不包含；毫秒时间戳，GTM+8时区）
  int32 leMinBuyQuantity = 67; //小于等于“最小购买数量”（起订量）
  int32 geMinBuyQuantity = 68; //大于等于“最小购买数量”（起订量）
  int32 sortField = 70; //排序字段（11:人气升序；12:人气倒序；21:销量升序；22:销量倒序；31:价格升序；32:价格倒序）
  int32 pageNo = 80; //页号（从1开始，默认1）
  int32 pageSize = 90; //每页显示条数（后端自动匹配到最接近的页面条数）
  int32 fetchCountLimit = 100; //查询条数限制（仅大于0的数字，限制条数起作用）
//  int32 fetchAggregationFlag = 110; //获取聚合参数表示位（参考：AggregationFlagEnum）
  int32 siteId = 120; //站点ID
  string visitorId = 130; //商城访客ID
  string userId = 140; //商城用户ID
  string abtestMode = 200; //AB测试方案（如：A，B，默认A）
  common.CurrencyType defaultCurrency = 601; //请求参数或返回中的默认货币单位
  common.CurrencyType requestCurrency = 602; //请求参数中的货币单位（若defaultCurrency留空，则必填）
  common.CurrencyType responseCurrency = 603; //响应结果中的货币单位（若defaultCurrency留空，则必填）
  bool random = 604; //是否随机
  repeated string tagIds = 605; //标签id
  bool canSearchDisabledPromotion = 208; //禁用状态的活动可搜索（默认不可搜索）
  string promotionId = 210; //促销活动ID（包括padc在内，仅支持一个促销活动查询）
  string padc = 88; //促销活动动态代码
}

message MidCalcStepPriceParam {
  repeated MidCalcStepPriceSkuParam skuList = 10;
  common.CurrencyType responseCurrency = 20; //响应结果中的货币单位（必填）
}

message MidCalcStepPriceSkuParam {
  string skuId = 10;
  int32 buyQty = 20;
  string padc = 88; //促销活动动态代码
}

message MidAddGoodsImpressParam {
  int32 pageSize = 10; //每页显示条数
  int32 totalHits = 20; //总共找到的条数
  map<string, int32> goodsImpressPostionMap = 30; //Key: 商品ID, Value: Position（Position从0开始）
}

message MidSetImpressConversionParam {
  string goodsId = 10; //商品ID
  int32 conversion = 20; //转化类型（GoodsImpressConversionEnum）
}

message MidRecommendGoodsParam {
  int32 goodsCount = 1; //返回多少个商品
  common.VisitDeviceType deviceType = 2; //访问设备类型
}

//用户专属活动推荐商品
message MidRecommendCustomerGoodsParam {
  string userId = 10; //用户ID
  int32 siteId = 20; //站点ID
}

//查询活动商品
message MidQueryActivityGoodsParam {
  string userId = 10; //用户ID
  int32 siteId = 20; //站点ID
  string promotionCode = 30; //促销活动代码
}

message UpdateAllGoodsVectorParam {
  string collectionName = 1;
  bool isAll = 2;
}

message UpdateGoodsVectorParam {
  string collectionName = 1;
  repeated string goodsIds = 2;
}

message GoodsVectorStatParam {
  string collectionName = 1;
}
