syntax = "proto3";
package chilat.basis;


option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/checkout_model.proto";
import "chilat/basis/param/checkout_param.proto";
import "common.proto";

// 订单结算服务
service Checkout {
    // 解析购买的SKU文本
    rpc parseBuySkuText(ParseBuySkuTextParam) returns (ParseBuySkuTextResp);

    // 预览购买的SKU列表
    rpc previewBuySkuList(PreviewBuySkuListParam) returns (PreviewBuySkuListResp);

}