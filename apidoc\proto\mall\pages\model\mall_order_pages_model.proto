syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";

import "chilat/basis/model/coupon_usable_model.proto";
import "common.proto";
import "common/business.proto";
import "mall/commodity/model/goods_info_model.proto";

//获取订单列表
message GetOrderListResp {
  common.Result result = 1; //错误码
  GetOrderListModel data = 2; //订单列表
}

message GetOrderListModel {
  common.Page page = 10; //分页信息
  repeated OrderInfoModel orderList = 20; //订单列表
}

message OrderInfoModel {
  string orderNo = 10; //订单号
  common.MallOrderStatus mallOrderStatus = 20; //订单状态
  string statusDesc = 21; //订单状态对应的文案描述
  repeated commodity.SkuModel skuList = 30;//商品列表, 为空表示该账单不包含产品价;
  int64 orderTime = 40; //下单时间戳
}

//获取订单详情
message GetOrderDetailResp {
  common.Result result = 1; //错误码
  GetOrderDetailModel data = 2;
}

message GetOrderDetailModel {
  string orderNo = 10; //订单号
  common.MallOrderStatus mallOrderStatus = 20; //订单状态
  string statusDesc = 21; //订单状态对应的文案描述
  int64 orderTime = 25; //下单时间,时间戳
  AddressInfoModel addressInfo = 30; //地址信息
  repeated BoxInfoModel boxList = 40; //装箱列表
  int32 totalCount = 50; //sku总数量
  string orderRemark = 70; //订单备注
  string merChantRemark = 71; //商家备注
  ProductAmountModel productAmount = 80; //产品成本
  repeated TransportAmountModel transportAmountList = 90; //运输线路与预估费用列表
  common.PayType payType = 100; //线上支付 or 线下支付, 对于部分不支持线上支付的国家或地区,返回线下支付
  common.QuotationMode quotationMode = 110; //报价模式
  common.PayMode payMode = 120; //支付模式
  int32 transportAmountType = 130; //1=使用单箱运费 0=使用总运费
  ProductCouponAmountModel productCouponAmount = 131;//产品券费用明细
  ComissCouponAmountModel comissCouponAmount = 132;//佣金券费用明细
  double productAddComissSumAmount = 135;//产品券+佣金券优惠总金额
  double paidAmount = 136;//实付款
  common.OrderSourcePlatform sourcePlatform = 160; //订单来源平台
  bool supportOnlinePay = 180; //是否支持在线支付（默认true）
}

message AddressInfoModel {
  string userName = 10; //用户名
  string phone = 20; //电话号码
  string address = 30; //地址
}

message ProductAmountModel {
  double amount = 10; //产品成本
  repeated FeeModel feeList = 20; //产品成本费用项
}

message FeeModel {
  string feeName = 10; //费用名称
  double feeAmount = 20; //费用金额
  repeated FeeModel childFeeList  = 30; //子费用
}

message TransportAmountModel {
  string transportId = 10; //线路id
  string name = 20; //线路名称
  double amount = 30; //预估费用
  repeated FeeModel amountDetailList = 40; //费用明细
  string expectDeliveryTime = 50; //预期交货时间
  string transportRemark = 60; //备注
}

message BoxInfoModel {
  int32 skuCount = 10; //sku件数-装箱数
  int32 boxCount = 20; //箱子数量
  repeated BoxTransportFeeModel boxTransportFeeList = 30; //箱运费
  repeated commodity.SkuModel skuList = 40; //sku列表
}

message BoxTransportFeeModel {
  string transportId = 10; //线路id
  double amount = 20; //运输费用
}

message PaymentModel {
  double amount = 20; //需要支付的金额
  common.PayType payType = 30; //0为线上支付, 1为线下支付
  string description = 40; //账单内容描述
  string amountRemark = 50; //金额备注
}

//查询收银台信息，包括待支付金额、支付方式列表、是否已支付
message GetCashDeskInfoResp {
  common.Result result = 10; //错误码
  GetCashDeskInfoModel data = 20; //数据
}

message GetCashDeskInfoModel {
  repeated PayMethodModel payMethodList = 10; //支付方式列表
  PaymentAmountModel payAmount = 20; //支付金额
  PayResultModel payResultModel = 30; //支付结果
  string goodsName = 40;
  string picUrl = 50;
  int32 totalCount = 60;
}

message PayMethodModel {
  string code = 10; //支付方式code
  string name = 20; //名称，显示用
  string iconUrl = 30; //支付方式图标icon url
  string id = 40; //支付方式唯一id
  double min = 50; //限额最低值
  double max = 60; //限额最大值
}

message PaymentAmountModel {
  double amount = 10;
  repeated FeeModel feeList = 20;
}

//查询支付结果
message QueryPayResultResp {
  common.Result result = 10; //错误码
  QueryPayResultModel data = 20; //返回数据
}

message QueryPayResultModel {
  PayResultModel payResult = 10;//支付结果
  common.MallOrderStatus mallOrderStatus = 20; //支付时所处订单状态
}

message PayResultModel {
  common.PayStatusEnum payStatus = 10; //返回支付结果
  string errorMsg = 20; //如果失败的话，返回失败原因
}

//未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
message OpenCashDeskResp {
  common.Result result = 10; //错误码
  OpenCashDeskModel data = 20; //返回数据
}

message OpenCashDeskModel {
  string paymentId = 10;
}

//提交支付
message SubmitPaymentResp {
  common.Result result = 10; //错误码
  SubmitPaymentModel data = 20; //返回数据
}

message SubmitPaymentModel {
  string payUrl = 10; //第三方支付的pay url，前端需要跳转过去
}

message ProductCouponAmountModel {
  double amount = 50; //
  repeated chilat.basis.CouponUsableDetailModel productCouponList = 60;//产品券集合
}

message ComissCouponAmountModel {
  double amount = 70; //
  repeated chilat.basis.CouponUsableDetailModel commissionCouponList = 80;//佣金券集合
}