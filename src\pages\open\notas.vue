<template>
  <div class="page-container">
    <div class="page-header">
      <video
        autoplay
        loop
        muted
        playsinline
        :poster="notasBg"
        :src="configStore.pageTheme.notasVideo"
        class="absolute top-0 left-0 w-full h-full object-cover z-0"
      ></video>
      <div
        class="absolute top-0 left-0 w-full h-full bg-black opacity-65 z-1"
      ></div>
      <div class="w-[1280px] mx-auto px-[96px] py-[26px] relative z-2">
        <div class="w-[184px] h-[42px]">
          <img
            loading="lazy"
            :src="configStore.pageTheme.chilatWhiteLogo"
            alt="logo"
            class="w-[184px]"
          />
        </div>

        <div
          class="w-[560px] text-[62px] leading-[68px] font-medium uppercase mt-[86px]"
        >
          <div>{{ authStore.i18n("cm_nota.makeIt") }}</div>
          <div>{{ authStore.i18n("cm_nota.import") }}</div>
          <div>
            {{ authStore.i18n("cm_nota.makeItMoreComfortable") }}
          </div>
        </div>
        <div class="w-[350px] text-[24px] leading-[24px] mt-[40px]">
          <span>{{ authStore.i18n("cm_nota.onlyComplete") }}&nbsp;</span>
          <span style="font-size: 44px; line-height: 52px">{{
            authStore.i18n("cm_nota.fourSteps")
          }}</span
          >,
          <span>{{ authStore.i18n("cm_nota.canComplete") }}</span>
        </div>
        <div
          class="text-[24px] leading-[24px] flex mt-[42px]"
          @click="scrollToNav('Paso1')"
        >
          <span class="mr-[10px]">{{ authStore.i18n("cm_nota.start") }}</span
          ><img
            loading="lazy"
            src="@/assets/icons/open/doubleArrow.svg"
            alt="down"
          />
        </div>
      </div>
    </div>
    <div class="text-[#333] w-[1280px] mx-auto" id="Paso1">
      <div class="text-[48px] leading-[58px] px-[42px] pt-[120px]">
        <div>
          {{ authStore.i18n("cm_nota.provideSolutions") }}
        </div>
        <img
          loading="lazy"
          src="@/assets/icons/open/arrow.svg"
          alt="down"
          class="w-[25px] mt-[60px] mx-auto"
        />
      </div>
      <div class="w-full px-[42px] mt-[52px]">
        <div class="flex justify-between">
          <div
            v-for="(item, index) in companyData"
            :key="index"
            class="number-item"
            @mouseover="handleMouseOver(index)"
            :class="
              pageData.activatedIndex === index ? 'activated-number-item' : ''
            "
          >
            <span class="text-[70px] leading-[70px]">{{ item.number }}</span>
            <div class="w-[200px] text-[18px] leading-[18px] mt-[20px]">
              {{ item.numberDesc }}
            </div>
          </div>
        </div>
        <div class="mt-[24px]">
          <n-space :style="{ gap: '0 4px' }">
            <img
              loading="lazy"
              alt="img"
              class="max-w-[500px] h-[330px]"
              v-for="(imageUrl, index) in companyData[pageData.activatedIndex]
                .imgData"
              :key="index"
              :src="imageUrl"
            />
          </n-space>
        </div>
        <div class="flex items-center">
          <n-button
            color="#E50113"
            @click="onHandleWhatsAppClick(1)"
            data-spm-box="potential_user_note_submit"
            class="w-[420px] h-[50px] mx-auto rounded-[500px] text-[20px] leading-[20px] font-medium mt-[70px]"
          >
            <div
              class="flex items-center justify-center"
              v-html="authStore.i18n('cm_nota.submitWhatsapp')"
            ></div>
          </n-button>
        </div>
      </div>
      <div class="w-full mt-[120px]">
        <div class="text-[48px] leading-[50px] px-[96px]">
          {{ authStore.i18n("cm_nota.importEasy") }}
        </div>
        <div
          class="text-[38px] leading-[48px] italic text-[#e50113] mt-[28px] px-[96px]"
        >
          {{ authStore.i18n("cm_nota.guidedByExperts") }}
        </div>
        <div class="mt-[100px] px-[96px]">
          <img
            loading="lazy"
            src="@/assets/icons/open/arrow.svg"
            alt="down"
            class="w-[25px] mx-auto"
          />
          <div
            class="text-[42px] leading-[42px] font-medium text-center mt-[30px]"
          >
            {{ authStore.i18n("cm_nota.stepOneSelection") }}
          </div>
          <div
            class="w-[304px] mx-auto text-[26px] leading-[26px] italic text-[#e50113] py-[14px] border border-[#D9D9D9] text-center mt-[38px] rounded-[4px]"
          >
            {{ authStore.i18n("cm_nota.chooseOneOfThree") }}
          </div>
          <div class="flex justify-between mt-[50px]">
            <div
              v-for="(item, index) in serviceData"
              :key="index"
              class="relative w-[354px] h-[484px] bg-white border border-[#333] rounded-[20px] pt-[252px] px-[26px]"
            >
              <img
                loading="lazy"
                :src="item.imgUrl"
                alt="visitChina"
                class="absolute top-0 left-0"
              />
              <div class="text-[22px] leading-[26px] font-medium">
                {{ item.title }}
              </div>
              <n-space
                vertical
                :style="{ gap: '8px 0' }"
                class="text-[18px] leading-[24px] mt-[14px]"
              >
                <div v-for="desc in item.descData" :key="desc">
                  <span v-html="newHighlightUSPrice(desc)"></span>
                </div>
              </n-space>
            </div>
          </div>
          <div class="flex items-center">
            <n-button
              color="#E50113"
              @click="onHandleWhatsAppClick(2)"
              data-spm-box="potential_user_note_submit"
              class="w-[420px] h-[50px] mx-auto rounded-[500px] text-[20px] leading-[20px] font-medium mt-[70px]"
            >
              <div
                class="flex items-center justify-center"
                v-html="authStore.i18n('cm_nota.submitWhatsapp')"
              ></div>
            </n-button>
          </div>
        </div>
        <div class="mt-[86px] px-[96px]">
          <img
            loading="lazy"
            src="@/assets/icons/open/arrow.svg"
            alt="down"
            class="w-[25px] mx-auto"
          />
          <div
            class="text-[42px] leading-[42px] font-medium text-center mt-[30px]"
          >
            {{ authStore.i18n("cm_nota.stepTwoSelection") }}
          </div>
          <div
            class="w-[304px] mx-auto text-[26px] leading-[26px] italic text-[#e50113] py-[14px] border border-[#D9D9D9] text-center mt-[38px] rounded-[4px]"
          >
            {{ authStore.i18n("cm_nota.chooseOneOfTwo") }}
          </div>
          <div class="flex justify-between mt-[50px] text-center">
            <div
              class="relative w-[524px] h-[380px] bg-white border border-[#333] rounded-[20px] pt-[250px] px-[26px]"
            >
              <img
                loading="lazy"
                :src="selfShipping"
                alt="visitChina"
                class="absolute top-0 left-0"
              />
              <div class="text-[22px] leading-[33px] font-medium">
                {{ authStore.i18n("cm_nota.arrangeTransport") }}
              </div>
            </div>
            <div
              class="relative w-[524px] h-[380px] bg-white border border-[#333] rounded-[20px] pt-[250px] px-[26px]"
            >
              <img
                loading="lazy"
                :src="doorToDoor"
                alt="visitChina"
                class="absolute top-0 left-0"
              />
              <div class="text-[22px] leading-[33px] font-medium">
                {{ authStore.i18n("cm_nota.doorToDoor") }}
              </div>
              <div class="mt-[18px] text-[18px] leading-[22px]">
                {{ authStore.i18n("cm_nota.logisticsCompany") }}
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <n-button
              color="#E50113"
              @click="onHandleWhatsAppClick(3)"
              data-spm-box="potential_user_note_submit"
              class="w-[420px] h-[50px] mx-auto rounded-[500px] text-[20px] leading-[20px] font-medium mt-[70px]"
            >
              <div
                class="flex items-center justify-center"
                v-html="authStore.i18n('cm_nota.submitWhatsapp')"
              ></div>
            </n-button>
          </div>
        </div>
        <div class="mt-[86px]">
          <img
            loading="lazy"
            src="@/assets/icons/open/greyArrow.svg"
            alt="down"
            class="w-[25px] mx-auto"
          />
          <div
            class="text-[42px] leading-[42px] font-medium text-center mt-[30px]"
          >
            {{ authStore.i18n("cm_nota.stepThreeSelection") }}
          </div>
          <div
            class="w-[260px] mx-auto text-[26px] leading-[26px] italic text-[#fff] py-[18px] text-center mt-[38px] rounded-[4px] bg-[#E50113] relative z-10"
          >
            {{ authStore.i18n("cm_nota.ourService") }}
          </div>
          <div
            class="w-full py-[80px] text-center bg-[#F2F2F2] px-[96px] rounded-[4px] mt-[-32px]"
          >
            <div class="flex justify-between">
              <div
                class="relative w-[524px] h-[380px] bg-white border border-[#333] rounded-[20px] pt-[282px] px-[26px]"
              >
                <img
                  loading="lazy"
                  :src="placeOrder"
                  alt="visitChina"
                  class="absolute top-0 left-0"
                />
                <div class="text-[22px] leading-[33px] font-medium">
                  {{ authStore.i18n("cm_nota.retrieveOrders") }}
                </div>
              </div>
              <div
                class="relative w-[524px] h-[380px] bg-white border border-[#333] rounded-[20px] pt-[282px] px-[26px]"
              >
                <img
                  loading="lazy"
                  :src="receiveQuality"
                  alt="visitChina"
                  class="absolute top-0 left-0"
                />
                <div class="text-[22px] leading-[33px] font-medium">
                  {{ authStore.i18n("cm_nota.goodsReception") }}
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <n-button
                color="#E50113"
                @click="onHandleWhatsAppClick(4)"
                data-spm-box="potential_user_note_submit"
                class="w-[420px] h-[50px] mx-auto rounded-[500px] text-[20px] leading-[20px] font-medium mt-[70px]"
              >
                <div
                  class="flex items-center justify-center"
                  v-html="authStore.i18n('cm_nota.submitWhatsapp')"
                ></div>
              </n-button>
            </div>
          </div>
        </div>
        <div class="mt-[86px] px-[96px] flex">
          <div class="flex-1 mr-[50px]">
            <div class="w-[516px]">
              <img
                loading="lazy"
                src="@/assets/icons/open/arrow.svg"
                alt="down"
                class="w-[25px] mx-auto"
              />
              <div class="text-[42px] leading-[42px] font-medium mt-[30px]">
                {{ authStore.i18n("cm_nota.stepFourPayment") }}
              </div>
            </div>
            <div
              class="text-[26px] leading-[26px] text-[#e50113] italic mt-[38px]"
            >
              {{ authStore.i18n("cm_nota.easyPayment") }}
            </div>
            <div class="text-[34px] leading-[50px] mt-[38px]">
              {{ authStore.i18n("cm_nota.paymentMethods") }}
            </div>
            <n-button
              color="#E50113"
              @click="onHandleWhatsAppClick(5)"
              data-spm-box="potential_user_note_submit"
              class="w-[420px] h-[50px] mx-auto rounded-[500px] text-[20px] leading-[20px] font-medium mt-[70px]"
            >
              <div
                class="flex items-center justify-center"
                v-html="authStore.i18n('cm_nota.submitWhatsapp')"
              ></div>
            </n-button>
          </div>
          <img
            loading="lazy"
            src="@/assets/icons/open/payIcons.png"
            alt="pay"
            class="w-[420px] h-[428px] flex-shrink-0"
          />
        </div>
      </div>
    </div>
    <div class="page-footer mt-[140px] w-[1280px] mx-auto">
      <div
        class="w-[540px] text-[42px] leading-[84px] font-semibold text-[#333] pt-[73px] pl-[40px] relative"
      >
        <span v-html="authStore.i18n('cm_nota.clickWhatsApp')"></span>
        <img
          loading="lazy"
          src="@/assets/icons/open/greenLine.svg"
          alt="line"
          class="absolute top-[139px] right-[26px]"
        />
      </div>
      <img
        loading="lazy"
        alt="click"
        class="icon"
        @click="onHandleWhatsAppClick('bottom')"
        src="@/assets/icons/open/whatsappClick.png"
      />
      <n-button
        color="#E50113"
        @click="onHandleWhatsAppClick('bottom')"
        data-spm-box="potential_user_note_submit"
        class="button"
      >
        <div
          class="flex items-center justify-center text-[34px] leading-[34px] font-semibold"
          v-html="authStore.i18n('cm_nota.clickToWhatsapp')"
        ></div>
      </n-button>
    </div>
    <!-- <div class="page-form mt-[100px] w-[1280px] mx-auto">
      <div class="text-[48px] leading-[82px] font-medium text-center">
        <div class="w-[fit-content] relative mx-auto">
          <div>{{ authStore.i18n("cm_nota.leaveDetails") }}</div>
          <img loading="lazy"
            src="@/assets/icons/open/line.svg"
            alt="line"
            class="h-[37px] absolute top-[64px] left-[12px]"
          />
        </div>
        <div>{{ authStore.i18n("cm_nota.offerExclusive") }}</div>
      </div>
      <n-form
        :rules="userRules"
        :model="userForm"
        ref="userFormRef"
        label-align="left"
        label-placement="top"
        class="mt-[50px] w-[850px]"
        require-mark-placement="left"
      >
        <div
          class="w-full py-[26px] px-[64px] border border-[#CCC] rounded-[20px] overflow-hidden"
        >
          <n-form-item
            path="contactName"
            label-width="140px"
            class="left-form-item"
            label-placement="left"
            :label="authStore.i18n('cm_nota.username')"
          >
            <n-input
              v-trim
              clearable
              maxlength="100"
              class="custom-input"
              @keydown.enter.prevent
              v-model:value="userForm.contactName"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.contactName,
                  authStore.i18n('cm_nota.username')
                )
              "
            />
          </n-form-item>
          <n-form-item
            path="countryId"
            label-width="140px"
            class="left-form-item"
            label-placement="left"
            :label="authStore.i18n('cm_nota.country')"
          >
            <n-select
              filterable
              class="custom-input"
              value-field="id"
              label-field="countryEsName"
              :options="pageData.countryList"
              v-model:value="userForm.countryId"
              @update:value="
                (value, option) =>
                  onSelectCountry(
                    value,
                    option,
                    authStore.i18n('cm_nota.country')
                  )
              "
              :placeholder="authStore.i18n('cm_nota.selectPlaceholder')"
            />
          </n-form-item>
          <n-form-item
            path="whatsapp"
            label-width="140px"
            class="left-form-item"
            label-placement="left"
            :label="authStore.i18n('cm_nota.whatsapp')"
          >
            <div class="!w-10">
              <span v-if="pageData?.countryRegexps?.areaCode">{{
                pageData.countryRegexps.areaCode
              }}</span>
              <span class="text-[#A6A6A6]" v-else>+000</span>
            </div>
            <n-divider vertical class="h-full" />
            <n-input
              v-trim
              clearable
              maxlength="64"
              class="custom-input"
              @keydown.enter.prevent
              v-model:value="userForm.whatsapp"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.whatsapp,
                  authStore.i18n('cm_nota.whatsapp')
                )
              "
            />
          </n-form-item>
          <n-form-item
            path="email"
            label-width="140px"
            class="left-form-item"
            label-placement="left"
            :label="authStore.i18n('cm_nota.email')"
          >
            <n-input
              v-trim
              clearable
              maxlength="64"
              class="custom-input"
              @keydown.enter.prevent
              v-model:value="userForm.email"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(userForm.email, authStore.i18n('cm_nota.email'))
              "
            />
          </n-form-item>
          <n-form-item
            path="userType"
            class="top-form-item"
            :label="authStore.i18n('cm_nota.userType')"
          >
            <div class="flex items-center">
              <n-radio-group
                class="relative"
                v-model:value="userForm.userType"
                :on-update:value="
                  (value) =>
                    onSelectEvent(
                      value,
                      'userType',
                      authStore.i18n('cm_nota.userType')
                    )
                "
              >
                <n-space>
                  <n-radio
                    v-for="item in userTypeList"
                    :value="item.value"
                    :key="item.value"
                  >
                    {{ item.label }}
                  </n-radio>
                </n-space>
              </n-radio-group>
              <n-form-item
                label=""
                path="userTypeRemark"
                class="inner-form-item"
                v-if="userForm.userType === 'POTENTIAL_USER_TYPE_OTHER'"
              >
                <n-input
                  round
                  v-trim
                  clearable
                  maxlength="200"
                  @keydown.enter.prevent
                  v-model:value="userForm.userTypeRemark"
                  :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                />
              </n-form-item>
            </div>
          </n-form-item>

          <n-form-item
            path="withImportExperience"
            class="top-form-item"
            :label="authStore.i18n('cm_nota.withImportExperience')"
          >
            <n-radio-group
              v-model:value="userForm.withImportExperience"
              :on-update:value="
                (value) =>
                  onSelectEvent(
                    value,
                    'withImportExperience',
                    authStore.i18n('cm_nota.withImportExperience')
                  )
              "
            >
              <n-space>
                <n-radio
                  v-for="item in withImportExperienceList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</n-radio
                >
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            path="serviceType"
            class="top-form-item"
            :label="authStore.i18n('cm_nota.serviceType')"
          >
            <n-radio-group
              class="mt-[4px]"
              v-model:value="userForm.serviceType"
              :on-update:value="
                (value) =>
                  onSelectEvent(
                    value,
                    'serviceType',
                    authStore.i18n('cm_nota.serviceType')
                  )
              "
            >
              <n-space vertical :style="{ gap: '9px 0' }" class="mb-[12px]">
                <n-radio
                  v-for="item in serviceTypeList"
                  :value="item.value"
                  :key="item.value"
                >
                  {{ item.label }}
                  <ul>
                    <li
                      v-for="desc in item.labelDesc"
                      :key="desc"
                      v-html="highlightUSPrice(desc)"
                    ></li>
                  </ul>
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            path="buyerRemark"
            :label="authStore.i18n('cm_nota.buyerRemark')"
            :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
            class="top-form-item !pb-[20px]"
          >
            <n-input
              v-trim
              clearable
              type="textarea"
              :maxlength="60000"
              class="rounded-[20px]"
              @keydown.enter.prevent
              v-model:value="userForm.buyerRemark"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.buyerRemark,
                  authStore.i18n('cm_nota.buyerRemark')
                )
              "
            />
          </n-form-item>
          <n-form-item
            path="captchaCode"
            class="mt-[30px]"
            :label="authStore.i18n('cm_nota.captchaCode')"
            :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
          >
            <n-image
              class="mr-4"
              :preview-disabled="true"
              :src="pageData.captchaImage"
              @click="onRefreshCaptcha"
            ></n-image>
            <n-input
              v-trim
              clearable
              :maxlength="100"
              class="rounded-[8px]"
              @keydown.enter.prevent
              v-model:value="userForm.captchaCode"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.captchaCode,
                  authStore.i18n('cm_nota.captchaCode'),
                  'captchaCode'
                )
              "
            />
          </n-form-item>
        </div>
        <div class="pt-[16px] pb-[42px] mt-[8px]">
          <n-button
            color="#E50113"
            @click="onSubmit"
            :loading="pageData.submitLoading"
            data-spm-box="potential_user_note_submit"
            class="w-full h-[50px] py-[20px] rounded-[20px] text-[18px] leading-[18px] font-medium"
          >
            <div
              class="flex items-center justify-center"
              v-html="authStore.i18n('cm_nota.onlineSubmit')"
            ></div>
          </n-button>
        </div>
      </n-form>
    </div> -->
    <div class="side-affix">
      <icon-card
        name="logos:whatsapp-icon"
        size="22px"
        title="W/app"
        :border="true"
        :multiple="true"
        @click="onHandleWhatsAppClick()"
      ></icon-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";
import transImg1 from "@/assets/icons/open/transImg1.png";
import transImg2 from "@/assets/icons/open/transImg2.png";
import transImg3 from "@/assets/icons/open/transImg3.png";
import serviceImg1 from "@/assets/icons/open/serviceImg1.png";
import serviceImg2 from "@/assets/icons/open/serviceImg2.png";
import serviceImg3 from "@/assets/icons/open/serviceImg3.png";
import customerImg1 from "@/assets/icons/open/customerImg1.png";
import customerImg2 from "@/assets/icons/open/customerImg2.png";
import customerImg3 from "@/assets/icons/open/customerImg3.png";
import busImg1 from "@/assets/icons/open/busImg1.png";
import busImg2 from "@/assets/icons/open/busImg2.png";
import busImg3 from "@/assets/icons/open/busImg3.png";
import visitChina from "@/assets/icons/open/visitChina.png";
import onlineSelection from "@/assets/icons/open/onlineSelection.png";
import vipCustomSelection from "@/assets/icons/open/vipCustomSelection.png";
import selfShipping from "@/assets/icons/open/selfShipping.png";
import doorToDoor from "@/assets/icons/open/doorToDoor.png";
import placeOrder from "@/assets/icons/open/placeOrder.png";
import receiveQuality from "@/assets/icons/open/receiveQuality.png";
import notasBg from "@/assets/icons/open/notasBg.png";

const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const configStore = useConfigStore();

const companyData = [
  {
    number: authStore.i18n("cm_nota.number1"),
    numberDesc: authStore.i18n("cm_nota.spanishStaff"),
    imgData: [transImg1, transImg2, transImg3],
  },
  {
    number: authStore.i18n("cm_nota.22Years"),
    numberDesc: authStore.i18n("cm_nota.focusedOnLatAm"),
    imgData: [serviceImg1, serviceImg2, serviceImg3],
  },
  {
    number: authStore.i18n("cm_nota.number2"),
    numberDesc: authStore.i18n("cm_nota.supportedClients"),
    imgData: [customerImg1, customerImg2, customerImg3],
  },
  {
    number: authStore.i18n("cm_nota.number3"),
    numberDesc: authStore.i18n("cm_nota.associatedSuppliers"),
    imgData: [busImg1, busImg2, busImg3],
  },
];

const serviceData = [
  {
    title: authStore.i18n("cm_nota.visitChina"),
    imgUrl: visitChina,
    descData: [
      authStore.i18n("cm_nota.visitChinaDesc"),
      authStore.i18n("cm_nota.visitChinaDesc2"),
    ],
  },
  {
    title: authStore.i18n("cm_nota.onlineSelection"),
    imgUrl: onlineSelection,
    descData: [
      authStore.i18n("cm_nota.onlineSelectionDesc"),
      authStore.i18n("cm_nota.onlineSelectionDesc2"),
    ],
  },
  {
    title: authStore.i18n("cm_nota.vipCustomSelection"),
    imgUrl: vipCustomSelection,
    descData: [
      authStore.i18n("cm_nota.vipCustomSelectionDesc"),
      authStore.i18n("cm_nota.vipCustomSelectionDesc2"),
    ],
  },
];

const userTypeList = [
  {
    value: "POTENTIAL_USER_TYPE_WHOLESALE",
    label: authStore.i18n("cm_nota.wholesale"),
  },
  {
    value: "POTENTIAL_USER_TYPE_RETAIL",
    label: authStore.i18n("cm_nota.retail"),
  },
  {
    value: "POTENTIAL_USER_TYPE_ONLINE_SHOP",
    label: authStore.i18n("cm_nota.personalUse"),
  },
  {
    value: "POTENTIAL_USER_TYPE_OTHER",
    label: authStore.i18n("cm_nota.other"),
  },
];

const withImportExperienceList = [
  {
    value: "true",
    label: authStore.i18n("cm_nota.yesExperience"),
  },
  {
    value: "false",
    label: authStore.i18n("cm_nota.noExperience"),
  },
];

const serviceTypeList = [
  {
    value: "POTENTIAL_SERVICE_TYPE_ONLINE_BUY",
    label: authStore.i18n("cm_nota.onlineSelection"),
    labelDesc: [
      authStore.i18n("cm_nota.onlineSelectionDesc1"),
      authStore.i18n("cm_nota.onlineSelectionDesc2"),
    ],
  },
  {
    value: "POTENTIAL_SERVICE_TYPE_VISIT_CHINA",
    label: authStore.i18n("cm_nota.visitChina"),
    labelDesc: [
      authStore.i18n("cm_nota.visitChinaDesc1"),
      authStore.i18n("cm_nota.visitChinaDesc2"),
    ],
  },
  {
    value: "POTENTIAL_SERVICE_TYPE_CUSTOMIZE_PRODUCT",
    label: authStore.i18n("cm_nota.vipCustomSelection"),
    labelDesc: [
      authStore.i18n("cm_nota.vipCustomSelectionDesc1"),
      authStore.i18n("cm_nota.vipCustomSelectionDesc2"),
    ],
  },
];

const userForm = reactive<any>({
  userType: null, // 用户类型
  userTypeRemark: null, // 用户类型备注（用户类型为“其它”，用户输入类型说明）
  withImportExperience: null, // 是否有进口经验（true:有，false:无）
  serviceType: null, // 服务类型
  contactName: null, // 联系人
  countryId: null, // 国家
  whatsapp: null, // whatsapp
  email: null, // 邮箱
  buyerRemark: null, // 留言
  captchaCode: null, // 验证码
});

const pageData = reactive(<any>{
  activatedIndex: 0,
  countryList: <any>[],
  countryRegexps: <any>{},
  submitLoading: false,
  captchaImage: null,
});

const userFormRef = ref<FormInst | null>(null);
const userRules: FormRules = {
  userType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  userTypeRemark: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
  withImportExperience: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  serviceType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  contactName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  whatsapp: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
    validator(rule: FormItemRule, value: any) {
      const lengths =
        pageData.countryRegexps.phoneCount &&
        pageData.countryRegexps.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths?.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  email: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any, callback: Function) {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      // 未填写邮箱
      if (!value) {
        return callback(new Error(authStore.i18n("cm_nota.inputPlaceholder")));
      }
      // 格式不正确
      if (!pattern.test(value)) {
        return callback(new Error(authStore.i18n("cm_common.emailTips")));
      }
      return callback();
    },
  },
  captchaCode: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
};

// onBeforeMount(() => {
//   if (config.public.defaultCountryCode === "US") {
//     return navigateTo({
//       path: "/",
//       query: {
//         spm: window.MyStat?.getSpmCode("potential-user-shunt-home"),
//       },
//     });
//   }
// });

await onGetCountry();
async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    if (config.public.defaultCountryCode) {
      res?.data.map((country: any) => {
        if (country.countryCodeTwo === config.public.defaultCountryCode) {
          userForm.countryId = country.id;
          pageData.countryRegexps = country;
          if (pageData.countryRegexps.phoneCount) {
            userRules["whatsapp"].message = `${authStore.i18n(
              "cm_submit.whatsappTips"
            )} ${pageData.countryRegexps.phoneCount} ${authStore.i18n(
              "cm_submit.whatsapp"
            )}`;
          }
        }
      });
    }
  }
}

onGetCaptchaImage();
async function onGetCaptchaImage() {
  const res: any = await useGetCaptchaImage({ captchaPageSource: "notas" });
  if (res?.result?.code === 200) {
    pageData.captchaImage = res?.data.imageData;
  }
}

function onRefreshCaptcha() {
  onGetCaptchaImage();
  userForm.captchaCode = "";
}

// 价格高亮  匹配 “US$: xxxx” 红色标红
function newHighlightUSPrice(desc: string) {
  const regex = /(US\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g;
  return desc.replace(
    regex,
    '<span style="color: #e50113;text-wrap: nowrap;font-weight:600">$1</span>'
  );
}

function highlightUSPrice(desc: string) {
  const regex = /(US\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g;
  return desc.replace(
    regex,
    '<span style="color: #e50113;text-wrap: nowrap;">$1</span>'
  );
}

function onSelectCountry(value: any, country: any, label: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${label} 选择：${country?.countryName} （原值：${
      pageData.countryRegexps.countryName || "无"
    }）`
  );

  pageData.countryRegexps = country;
  // 如果有长度校验 则校验长度
  if (pageData.countryRegexps.phoneCount) {
    userRules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappTips"
    )} ${pageData.countryRegexps.phoneCount} ${authStore.i18n(
      "cm_submit.whatsapp"
    )}`;
  } else {
    // 没有长度校验 校验必填
    userRules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappRequired"
    )}`;
  }
}

async function onSubmit(event: any) {
  try {
    // 校验表单
    const isValid = await userFormRef.value?.validate();
    if (isValid) {
      pageData.submitLoading = true;
      try {
        loadReCAPTCHA("submit", async function (success, token) {
          let paramsObj = {
            ...userForm,
            userSource: !!route?.query?.utm_source
              ? "筛选器一重筛选_" + route?.query?.utm_source
              : "筛选器一重筛选",
            reCaptchaLoadSuccess: success,
            reCaptchaToken: token,
            captchaPageSource: "notas",
          };
          const res: any = await useSaveUserInfo(paramsObj);
          pageData.submitLoading = false;
          const areaCode = pageData.countryRegexps.areaCode;
          if (res?.result?.code === 200) {
            window?.MyStat?.addPageEvent(
              "potential_user_submit_success",
              `保存潜客信息成功，顺序号：${res?.data?.seqNo}`
            );
            // if (userForm.serviceType === "POTENTIAL_SERVICE_TYPE_ONLINE_BUY") {
            navigateTo(res?.data?.whatsAppUrl, {
              external: true,
            });
            // } else {
            //   navigateTo({
            //     path: "/notas/success",
            //     query: {
            //       serviceType: userForm.serviceType,
            //       contactName: userForm.contactName,
            //       whatsapp: areaCode + userForm.whatsapp,
            //       spm: window.MyStat.getPageSPM(event),
            //     },
            //   });
            // }
          } else {
            if (res?.result?.code === 952401) {
              onRefreshCaptcha();
              showToast(authStore.i18n("cm_nota.captchaCodeError"));
            } else {
              showToast(res?.result?.message);
            }
            window?.MyStat?.addPageEvent(
              "potential_user_submit_error",
              `潜客信息表单错误：${res?.result?.message}`
            );
          }
        });
      } catch (error) {
        pageData.submitLoading = false;
      }
    }
  } catch (error) {
    const remark = `${error?.[0]?.[0]?.message} [${error?.[0]?.[0]?.field}]`;
    window?.MyStat?.addPageEvent(
      "potential_user_submit_error",
      `潜客信息表单错误：${remark}`
    );
  }
}

// 下拉选择埋点事件
function onSelectEvent(value: string, attr: any, label: any) {
  userForm[attr] = value;
  let list = <any>[];
  if (attr === "userType") {
    list = userTypeList;
  }
  if (attr === "withImportExperience") {
    list = withImportExperienceList;
  }
  if (attr === "serviceType") {
    list = serviceTypeList;
  }
  const match = list.find((item: any) => item.value === value);
  if (!value) return;
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${label} 选择：${match?.label}`
  );
}

// 输入框埋点事件
async function onBlurEvent(value: string, label: any, form: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_input",
    `${label} 输入：${value}`
  );
  if (form === "captchaCode") {
    const res: any = await useCheckCaptchaImage({
      captchaPageSource: "notas",
      captchaCode: value,
    });
    if (res?.result?.code === 952401) {
      onRefreshCaptcha();
      showToast(authStore.i18n("cm_nota.captchaCodeError"));
    }
  }
}

function handleMouseOver(index: number) {
  pageData.activatedIndex = index;
}

function scrollToNav(dom: any) {
  if (!dom) return;
  const element = document.getElementById(dom);
  if (!element) return;
  const elementRect = element.getBoundingClientRect();
  const offsetPosition = elementRect.top + window.scrollY;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth",
  });
}

async function onHandleWhatsAppClick(val?: any) {
  let clickSource = "open/notas页面悬浮按钮点击WhatsApp";
  if (val) {
    if (val === "bottom") {
      clickSource = `open/notas页面正文区底部按钮点击WhatsApp`;
    } else {
      clickSource = `open/notas页面正文区第${val}个按钮点击WhatsApp`;
    }
  }
  window?.MyStat?.addPageEvent(
    "potential_user_click_whatsapp",
    clickSource,
    true
  );
  loadReCAPTCHA("submit", async function (success: any, token: any) {
    let paramsObj = {
      userSource: !!route?.query?.utm_source
        ? "筛选器一重筛选_" + route?.query?.utm_source
        : "筛选器一重筛选",
      reCaptchaLoadSuccess: success,
      reCaptchaToken: token,
      clickSource,
    };
    const res: any = await useClickWhatsapp(paramsObj);
    if (res?.result?.code === 200) {
      onWhatsAppClick();
    } else {
      showToast(res?.result?.message);
    }
  });
}
</script>

<style scoped lang="scss">
.page-container {
  min-width: 1280px;
  height: auto;
  min-height: 100vh;
  color: #333;
}

.page-header {
  position: relative;
  width: 100%;
  height: 600px;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/open/notasBg.png");
}

.number-item {
  padding-left: 12px;
  position: relative;
  height: fit-content;
  cursor: pointer;
  &::before {
    background-color: #333;
    border-radius: 2px;
    content: "";
    display: inline-block;
    height: 100%;
    left: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
  }
}
.activated-number-item {
  color: #e50113;
  &::before {
    background-color: #e50113;
  }
}

.page-form {
  border-radius: 20px 20px 0px 0px;
  padding: 40px 238px;
  ul {
    margin-top: 10px;
    list-style-type: disc;
    color: #7f7f7f;
    font-size: 13px;
    line-height: 18px;
    background-color: #f2f2f2;
    padding: 10px 16px 6px 32px;
    border-radius: 20px;
    li {
      margin-bottom: 4px;
      font-size: 14px;
      line-height: 18px;
    }
  }

  :deep(.n-form-item-label__text) {
    font-weight: 500;
  }
  .left-form-item {
    padding-top: 30px;
    padding-bottom: 10px;
    position: relative;
    border-bottom: 1px solid #e6e6e6;
    :deep(.n-form-item-feedback-wrapper) {
      position: absolute;
      bottom: -36px;
      left: -140px;
      color: #e50113;
    }
  }
  .top-form-item {
    padding-top: 30px;
    padding-bottom: 10px;
    position: relative;
    border-bottom: 1px solid #e6e6e6;
    :deep(.n-form-item-feedback-wrapper) {
      position: absolute;
      bottom: -36px;
      left: 0;
      color: #e50113;
    }
  }

  .inner-form-item {
    width: 250px;
    height: 30px;
    line-height: 30px;
    display: flex;
    margin-bottom: 6px;
    margin-left: 6px;
    :deep(.n-input-wrapper) {
      height: 30px;
      line-height: 30px;
      width: 250px !important;
    }
    :deep(.n-form-item-blank) {
      height: 30px;
      line-height: 30px;
      width: 250px !important;
    }
    :deep(.n-input__input-el) {
      height: 30px;
      line-height: 30px;
    }
  }

  .custom-input {
    border: none !important;
    --n-border: none !important;
    --n-border-warning: none;
    --n-border-focus-warning: none;
    --n-border-hover-warning: none;
    --n-border: none;
    --n-border-disabled: none;
    --n-border-hover: none;
    --n-border-focus: none;
    --n-box-shadow-focus: none;
    --n-box-shadow-focus-warning: none;
    --n-border-error: none;
    --n-border-focus-error: none;
    --n-border-hover-error: none;
    --n-box-shadow-focus-error: none;
    --n-border-active-error: none;
    --n-box-shadow-active: none;
    --n-border-active: none;
    :deep(.n-base-selection) {
      border: none !important;
      --n-border: none !important;
      --n-border-warning: none;
      --n-border-focus-warning: none;
      --n-border-hover-warning: none;
      --n-border: none;
      --n-border-disabled: none;
      --n-border-hover: none;
      --n-border-focus: none;
      --n-box-shadow-focus: none;
      --n-box-shadow-focus-warning: none;
      --n-border-error: none;
      --n-border-focus-error: none;
      --n-border-hover-error: none;
      --n-box-shadow-focus-error: none;
      --n-border-active-error: none;
      --n-box-shadow-active: none;
      --n-border-active: none;
    }
  }
}
.side-affix {
  position: fixed;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}

.page-footer {
  position: relative;
  height: 573px;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/open/whatsappBg.png");
  .icon {
    position: absolute;
    top: -73px;
    width: 268px;
    right: 16px;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 63px;
    right: 40px;
    height: fit-content;
    display: inline-flex;
    padding: 28px 64px;
    align-items: center;
    gap: 8px;
    border-radius: 1200px;
    border: 6px solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.5);
  }
}
</style>
