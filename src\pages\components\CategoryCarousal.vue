<template>
  <div
    :data-spm-box="props.selector?.spmCode"
    :data-spm-param="
      props.selector?.tagId || props.selector?.selectorId || null
    "
  >
    <a
      v-if="linkUrl"
      :href="linkUrl"
      target="_blank"
      class="flex items-end cursor-pointer"
    >
      <img
        loading="lazy"
        v-if="props.selector?.bannerUrl"
        :src="props.selector?.bannerUrl"
        :alt="
          props.selector?.name ||
          props.selector?.selectorName ||
          props.selector?.title
        "
        class="mb-[32px]"
        referrerpolicy="no-referrer"
      />
    </a>

    <div class="mb-[24px] flex items-center justify-between">
      <a
        v-if="linkUrl"
        :href="linkUrl"
        target="_blank"
        class="flex items-end cursor-pointer"
      >
        <div class="text-[28px] leading-[28px] font-medium mr-[34px]">
          {{
            props.selector?.name ||
            props.selector?.selectorName ||
            props.selector?.title
          }}
        </div>
        <n-ellipsis class="text-[18px] leading-[18px] underline mb-[2px]">
          {{ authStore.i18n("cm_app.viewMore") }}
        </n-ellipsis>
      </a>
      <div class="custom-arrow flex gap-[12px]">
        <img
          loading="lazy"
          alt="Left Arrow"
          @click="prevRecommendGoods"
          class="custom-arrow w-[34px] h-[34px]"
          src="@/assets/icons/common/arrow-left.svg"
          referrerpolicy="no-referrer"
        />
        <img
          loading="lazy"
          alt="Right Arrow"
          @click="nextRecommendGoods"
          src="@/assets/icons/common/arrow-left.svg"
          class="custom-arrow w-[34px] h-[34px] transform rotate-180"
          referrerpolicy="no-referrer"
        />
      </div>
    </div>
    <!-- 商品列表 -->
    <div class="recommend-goods">
      <n-carousel ref="recommendCarousel" :autoplay="true" :dots="false">
        <n-carousel-item
          v-for="(item, index) in props.selector?.goodsList"
          :key="index"
        >
          <n-grid :cols="6">
            <n-grid-item
              v-for="(goods, Iindex) in item"
              :key="goods.id || goods.goodsId"
            >
              <home-goods-card
                :goods="goods"
                :imageWidth="props.imageWidth"
                :imageHeight="props.imageHeight"
                :goodsIndex="Iindex + 1"
              ></home-goods-card>
            </n-grid-item>
          </n-grid>
        </n-carousel-item>
      </n-carousel>
    </div>
  </div>
</template>
<script setup lang="ts">
import HomeGoodsCard from "./HomeGoodsCard.vue";
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();

const props = defineProps({
  selector: {
    type: Object,
    default: () => {},
  },
  spmCode: {
    type: String,
    default: "",
  },
  imageWidth: {
    type: String,
    default: "188px",
  },
  imageHeight: {
    type: String,
    default: "188px",
  },
});

const linkUrl = computed(() => {
  const selector = props.selector;
  if (!selector) return "/goods/list/all";

  const { selectorType, selectorId, tagId, activityId, padc } = selector;
  const params = new URLSearchParams();

  if (activityId) {
    if (selectorType === "GOODS_SELECTOR_TYPE_MARKETING_RULE") {
      params.append("marketingRuleId", selectorId);
    } else {
      params.append("tagId", selectorId);
    }
    params.append("activityId", activityId);
  } else if (tagId) {
    params.append("tagId", tagId);
  } else if (padc) {
    params.append("padc", padc);
  }

  const queryString = params.toString();
  return `/goods/list/all${queryString ? `?${queryString}` : ""}`;
});

const recommendCarousel = ref<any>(null);

function prevRecommendGoods() {
  recommendCarousel.value?.prev();
}
function nextRecommendGoods() {
  recommendCarousel.value?.next();
}
</script>

<style scoped lang="scss">
.custom-arrow {
  display: flex;
  cursor: pointer;
  .custom-arrow {
    &:hover {
      content: url("@/assets/icons/common/arrow-left-active.svg");
    }
  }
}
:deep(.n-carousel .n-carousel__slides .n-carousel__slide) {
  padding: 8px;
}
</style>
