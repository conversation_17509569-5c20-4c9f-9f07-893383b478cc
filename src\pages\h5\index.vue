<template>
  <div class="mobile-container">
    <seo-data :pageData="pageData"></seo-data>
    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>

    <!-- 已登录首页 -->
    <!-- <div v-if="userInfo?.username"> -->
    <!-- 轮播 -->
    <n-carousel
      autoplay
      :transition-style="{ transitionDuration: '500ms' }"
      class="home-header-carousel bg-white"
    >
      <n-image
        lazy
        preview-disabled
        class="carousel-img"
        :src="carousel"
        v-for="(carousel, index) in carouselData"
        :key="index"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
    </n-carousel>
    <div class="news-wrapper bg-white" data-spm-box="homepage-welcome-bar">
      <a v-for="(news, index) in newsData" :key="index" :href="news.path">
        <div
          class="news-item"
          :style="`background-color: ${cateColorArr[index].cateColor}`"
        >
          <div>{{ news.title }}</div>
          <icon-card :name="news.icon" size="30" color="#FFF"></icon-card></div
      ></a>
    </div>
    <div
      class="flex justify-center items-center px-[0.1rem] py-[0.3rem] text-[#e50113] text-[0.28rem] bg-[#f4f4f4]"
    >
      <img
        loading="lazy"
        :src="noticeLogo"
        class="mr-[0.1rem] w-[0.52rem]"
        referrerpolicy="no-referrer"
      />
      {{ authStore.i18n("cm_guestHome.noticeTitle") }}
    </div>

    <!-- 一站式购物服务-->
    <div class="service-wrapper">
      <ul class="service-item-wrapper">
        <li
          class="service-item"
          v-for="(link, index) in serviceData"
          :key="index"
          @mouseenter="onLinkHover(index)"
          :class="{
            'service-item-enter': pageData.activatedLinkIndex == index,
          }"
          :click-title="`${index + 1}. ${link.title} `"
        >
          <div class="item-circle"></div>
          <div class="item-content-wrapper">
            <div class="item-title flex items-center">
              <img
                loading="lazy"
                :alt="link.title"
                class="w-[0.48rem] h-[0.48rem] mr-[0.08rem]"
                :src="
                  pageData.activatedLinkIndex == index ? link.imgAc : link.img
                "
                referrerpolicy="no-referrer"
              />
              {{ link.title }}
            </div>
            <div class="item-desc ml-[0.3rem]">
              <div class="item-content">
                <n-space vertical :style="{ gap: '0.16rem 0' }">
                  <div
                    v-for="(content, contentIndex) in link.content"
                    :key="contentIndex"
                  >
                    {{ content }}
                  </div>
                </n-space>
              </div>
            </div>
          </div>
        </li>

        <div class="item-tail"></div>
      </ul>
    </div>

    <div class="mt-[0.48rem]">
      <template v-if="pageData.customerExclusiveActivities.length">
        <category-card
          data-spm-box="homepage-exclusive-activity"
          v-for="(item, index) in pageData.customerExclusiveActivities"
          :key="index"
          :cateColor="cateColorArr[index]"
          :cateInfo="item"
        ></category-card>
      </template>
      <template v-if="pageData.topRecommendActivities.length">
        <category-card
          data-spm-box="homepage-exclusive-activity"
          v-for="(item, index) in pageData.topRecommendActivities"
          :key="index"
          :cateColor="cateColorArr[index]"
          :cateInfo="item"
        ></category-card>
      </template>
      <!-- 热销货盘 -->
      <category-card
        :cateColor="
          cateColorArr[
            pageData.customerExclusiveActivities.length +
              pageData.topRecommendActivities.length
          ]
        "
        :cateInfo="pageData.recommendPackingGoods"
      ></category-card>
    </div>

    <!-- 热销货盘集合 -->
    <div class="px-[0.2rem] pt-[0.28rem] pb-[1.2rem] bg-white">
      <img
        loading="lazy"
        class="w-full"
        src="@/assets/icons/home/<USER>"
        alt="Descubra su próxima oportunidad de negocio"
        referrerpolicy="no-referrer"
      />
      <div class="flex gap-[0.22rem] mt-[0.36rem]">
        <a
          data-spm-box="homepage-banner-yiwu"
          href="/h5/selector?code=yiwu-index"
        >
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            alt="Los productos populares del Mercado de Yiwu"
            class="rounded-[0.08rem]"
            referrerpolicy="no-referrer"
          />
        </a>
        <a
          href="/selector?code=festival-index"
          data-spm-box="homepage-banner-festival"
        >
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            alt="Lista de los productos de fiesta más vendidos"
            class="rounded-[0.08rem]"
            referrerpolicy="no-referrer"
          />
        </a>
      </div>
    </div>
    <div class="relative w-full">
      <img src="@/assets/icons/home/<USER>" alt="" class="w-full" />
      <div
        class="w-[7.5rem] h-[14.16rem] absolute top-0 right-0 pt-[6.8rem] text-center"
      >
        <div class="flex flex-col items-center gap-[0.24rem]">
          <img
            class="w-[0.68rem] h-[0.68rem]"
            src="@/assets/icons/home/<USER>"
            :alt="authStore.i18n('cm_home.quickPreciseSearch')"
          />
          <div class="text-[0.32rem] leading-[0.48rem] text-[#11263B]">
            {{ authStore.i18n("cm_home.quickPreciseSearch") }}
          </div>
        </div>
        <div class="flex flex-col items-center gap-[0.24rem] mt-[0.6rem]">
          <img
            class="w-[0.68rem] h-[0.68rem]"
            src="@/assets/icons/home/<USER>"
            :alt="authStore.i18n('cm_home.fullSpanishSupport')"
          />
          <div class="text-[0.32rem] leading-[0.48rem] text-[#11263B]">
            {{ authStore.i18n("cm_home.fullSpanishSupport") }}
          </div>
        </div>
        <a
          href="/h5/search/looking"
          class="custom-search mt-[1.4rem]"
          data-spm-box="button-find-homepage-mid"
        >
          <img
            class="w-[0.48rem] h-[0.48rem]"
            src="@/assets/icons/home/<USER>"
            :alt="authStore.i18n('cm_home.guidedSearchInit')"
          />
          <div class="text-[0.36rem] leading-[0.36rem]">
            {{ authStore.i18n("cm_home.guidedSearchInit") }}
          </div>
          <img
            alt="arrow"
            class="w-[0.2rem]"
            src="@/assets/icons/common/arrow-right-white.svg"
          />
        </a>
      </div>
    </div>
    <category-card
      :cateColor="
        cateColorArr[
          pageData.customerExclusiveActivities.length +
            pageData.topRecommendActivities.length +
            1
        ]
      "
      :cateInfo="pageData.habitableCapsuleGoods"
    ></category-card>

    <!-- “优质供应商”商品推荐 -->
    <category-card
      :cateColor="
        cateColorArr[
          pageData.customerExclusiveActivities.length +
            pageData.topRecommendActivities.length +
            2
        ]
      "
      :cateInfo="pageData.recommendSupplierGoods"
    ></category-card>

    <!-- 美客多,开学季,义乌推荐货盘-->
    <div v-for="(bannerConfig, index) in pageData.bannerConfigs" :key="index">
      <category-card :cateInfo="bannerConfig"></category-card>
    </div>

    <!-- 分类 -->
    <div>
      <div
        v-for="(categoryConfig, index) in pageData.categoryConfigs"
        :key="index"
      >
        <category-card
          :cateInfo="categoryConfig"
          :cateColor="cateColorArr[index + 5]"
        ></category-card>
      </div>
    </div>

    <!-- 为什么选择我们？ -->
    <div class="full-link">
      <div class="full-title">
        {{ authStore.i18n("cm_guestHome.fullTitle") }}
      </div>
      <ul class="full-link_item_wrapper">
        <li
          class="full-link_item"
          v-for="(link, index) in chooseData"
          :key="index"
        >
          <div class="full-link_content">
            <div class="full-link_item_title">
              <span class="text-[#e50113] text-[0.32rem]"
                >0{{ index + 1 }}</span
              >
              {{ link.title }}
            </div>
            <div class="full-link_item_desc">
              {{ link.desc }}
            </div>
          </div>
        </li>
      </ul>
      <div>
        <n-carousel
          autoplay
          show-dots
          style="height: 4.2rem; overflow: hidden"
          :space-between="10"
          :transition-style="{ transitionDuration: '500ms' }"
          :interval="3000"
        >
          <n-image
            lazy
            preview-disabled
            :src="carousel"
            v-for="(carousel, index) in carouselChooseData"
            :key="index"
            class="h-[4.2rem]"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
        </n-carousel>
      </div>
    </div>

    <!-- 注册登录 -->
    <div class="login-guide" :style="`background-image: url(${loginBg})`">
      <div class="login-guide-wrapper">
        <div class="login-title">
          {{ authStore.i18n("cm_guestHome.loginTitle") }}
        </div>
        <div class="login-desc">
          {{ authStore.i18n("cm_guestHome.loginDesc") }}
        </div>
        <a
          href="/h5/user/register?pageSource=/h5"
          data-spm-box="homepage-body-register"
        >
          <n-button color="#db2221" class="section_banner-button">
            {{ authStore.i18n("cm_common.createAccount") }}
          </n-button>
        </a>
        <div class="mt-[0.2rem] text-[0.28rem]">
          {{ authStore.i18n("cm_common.haveAccount") }}
          <a
            href="/h5/user/login?pageSource=/h5"
            data-spm-box="homepage-body-login"
          >
            <span class="text-[#e50113] text-[0.32rem]">{{
              authStore.i18n("cm_common.loginAccount")
            }}</span>
          </a>
        </div>
      </div>
    </div>

    <!-- 用户评价 -->
    <div class="user-video">
      <div class="video-title">
        {{ authStore.i18n("cm_guestHome.userVideoTitle") }}
      </div>
      <div class="video-wrapper">
        <div
          class="video-item"
          v-for="(video, index) in userVideoData"
          :key="video.id"
          @click="onOpenVideo(video, index)"
        >
          <n-image
            lazy
            preview-disabled
            :src="video.videoBg"
            class="img"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
          <div class="video-icon">
            <icon-card
              name="mingcute:play-fill"
              size="20"
              color="#322623"
            ></icon-card>
          </div>
        </div>
      </div>
    </div>
    <!-- </div> -->

    <!-- 未登录首页 -->
    <!-- <template v-if="!userInfo?.username">
      <guest-home
        v-if="abtestMode === 'A'"
        :yiwuHotSaleGoods="pageData.yiwuHotSaleGoods"
        :mercadoHotSaleGoods="pageData.mercadoHotSaleGoods"
      ></guest-home>
      <new-guest-home
        v-else
        :yiwuHotSaleGoods="pageData.yiwuHotSaleGoods"
        :mercadoHotSaleGoods="pageData.mercadoHotSaleGoods"
      ></new-guest-home>
    </template> -->

    <!-- 底部信息 -->
    <mobile-page-footer></mobile-page-footer>
  </div>
  <!-- 底部栏 -->
  <mobile-tab-bar :naiveBar="0" />
  <!-- 视频播放 -->
  <video-modal ref="videoModalRef"></video-modal>
  <!-- 未登录优惠券弹窗 -->
  <login-register-modal ref="loginRegisterModalRef"></login-register-modal>
  <!-- 假期通知弹框 -->
  <!-- <holiday-notice-modal></holiday-notice-modal> -->
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import CategoryCard from "./components/CategoryCard.vue";
import LoginRegisterModal from "./components/LoginRegisterModal.vue";
import HolidayNoticeModal from "@/pages/components/HolidayNoticeModal.vue";
import { cateColorArr } from "@/utils/constant";
import backSchoolHotBanner from "@/assets/icons/backSchoolHotBanner.jpg";

import homeCarouselThousands from "@/assets/icons/home/<USER>";
import homeCarouselFactories from "@/assets/icons/home/<USER>";
import homeCarouselUnique from "@/assets/icons/home/<USER>";
import homeCarouselReward from "@/assets/icons/home/<USER>";
import homeCarouselDiscount from "@/assets/icons/home/<USER>";
// import homeCarouselLive from "@/assets/icons/home/<USER>";

import list from "@/assets/icons/home/<USER>";
import listAc from "@/assets/icons/home/<USER>";
import money from "@/assets/icons/home/<USER>";
import moneyAc from "@/assets/icons/home/<USER>";
import box from "@/assets/icons/home/<USER>";
import boxAc from "@/assets/icons/home/<USER>";
import transport from "@/assets/icons/home/<USER>";
import transportAc from "@/assets/icons/home/<USER>";

const loginBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/27/afb3f499-42c5-4ca4-8f0d-760e36a4b591.png";
const noticeLogo =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/268c3fd6-85ef-4db5-8640-549d908d570b.png";

const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<object>({});
const abtestMode = ref<string>("");
const videoModalRef = ref<any>(null);
const pageData = reactive(<any>{
  activatedLinkIndex: 0,
  categoryConfigs: <any>[],
  bannerConfigs: <any>[],
  recommendPackingGoods: <any>{},
  recommendSupplierGoods: <any>{},
  habitableCapsuleGoods: <any>{},
  customerExclusiveActivities: [], //客户专属活动
  topRecommendActivities: [], //头部推荐活动列表
});

abtestMode.value = config.public.abtestMode as string;
userInfo.value = config.public.userInfo as object;

/**
 * 为了解决轮播图在服务端渲染到浏览器渲染时，从最后一张切换到第一张可能出现的视觉跳动问题：
 * - 服务端渲染时在尾部增加首张图片
 * - 浏览器渲染时移除尾部的重复图片
 */
const carouselData = [
  homeCarouselFactories,
  homeCarouselDiscount,
  homeCarouselUnique,
  homeCarouselReward,
  homeCarouselThousands,
  homeCarouselFactories,
];

const newsData = [
  {
    icon: "mdi:information-variant-circle",
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/h5/article/about-us",
  },
  {
    icon: "mdi:compass",
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/h5/article/quick-guide",
  },
  {
    icon: "material-symbols:help",
    title: authStore.i18n("cm_news.askedQuestions"),
    path: `/h5/article/frequently-questions`,
  },
  {
    icon: "f7:money-dollar-circle-fill",
    title: authStore.i18n("cm_news.invitedReward"),
    path: "/h5/article/invite",
  },
];

const serviceData = [
  {
    img: list,
    imgAc: listAc,
    title: authStore.i18n("cm_guestHome.chooseGoods"),
    content: [
      authStore.i18n("cm_guestHome.addGoods"),
      authStore.i18n("cm_guestHome.orderGoods"),
    ],
  },
  {
    img: money,
    imgAc: moneyAc,
    title: authStore.i18n("cm_guestHome.confirmPrice"),
    content: [
      authStore.i18n("cm_guestHome.countPrice"),
      authStore.i18n("cm_guestHome.predictPrice"),
      authStore.i18n("cm_guestHome.payPrice"),
    ],
  },
  {
    img: box,
    imgAc: boxAc,
    title: authStore.i18n("cm_guestHome.payProduct"),
    content: [
      authStore.i18n("cm_guestHome.transProduct"),
      authStore.i18n("cm_guestHome.checkProduct"),
      authStore.i18n("cm_guestHome.storageProduct"),
    ],
  },
  {
    img: transport,
    imgAc: transportAc,
    title: authStore.i18n("cm_guestHome.interLogistics"),
    content: [
      authStore.i18n("cm_guestHome.chooseLogistics"),
      authStore.i18n("cm_guestHome.trackLogistics"),
      authStore.i18n("cm_guestHome.confirmLogistics"),
    ],
  },
];

const chooseData = [
  {
    title: authStore.i18n("cm_guestHome.brandTitle"),
    desc: authStore.i18n("cm_guestHome.brandDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.teamTitle"),
    desc: authStore.i18n("cm_guestHome.teamDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.resourceTitle"),
    desc: authStore.i18n("cm_guestHome.resourceDesc"),
  },
];

const carouselChooseData = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
  },
  {
    id: "Tj0nrnhxgXw",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
  },
  {
    id: "_omi5a-pHkA",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
  },
  {
    id: "4FVIz0PvEcE",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
  },
];

onBeforeMount(() => {
  // 初始化轮播图数据数组，确保首尾没有重复元素
  if (
    carouselData.length > 1 &&
    carouselData[0] === carouselData[carouselData.length - 1]
  ) {
    carouselData.pop();
  }
});

onMounted(async () => {
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
});

await loadPageData();
async function loadPageData() {
  try {
    await Promise.all([onHomePageData(), onRecommendGoods()]);
  } catch (error) {
    console.error("Error loading page data:", error);
  }
}

async function onHomePageData() {
  const res: any = await useHomePageData({});
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
  }
}

// 推荐商品
async function onRecommendGoods() {
  const HOME_CAPSULE_PRODUCT_ID =
    useRuntimeConfig().public.HOME_CAPSULE_PRODUCT_ID;
  const res: any = await useRecommendGoodsV2({
    goodsCount: 24,
    deviceType: 2,
    siteId: window?.siteData?.siteInfo?.id,
    goodsPlugTagParams: [
      {
        tagId: HOME_CAPSULE_PRODUCT_ID,
      },
    ],
  });
  if (res?.result?.code === 200) {
    // 客户专属活动
    pageData.customerExclusiveActivities =
      res.data?.customerExclusiveActivities || [];

    // 头部推荐活动列表(派送国家为秘鲁时，展示)
    if (window?.siteData?.siteInfo?.id == config.public.peruCountryId) {
      pageData.topRecommendActivities = res.data?.topRecommendActivities || [];
    }

    pageData.recommendPackingGoods = {
      spmCode: "tag-goods-packing", // 今日特惠
      tagName: authStore.i18n("cm_home.todaySpecial"),
      tagId: res.data?.recommendPackingGoods?.tagId,
      goodsList: res.data?.recommendPackingGoods?.goodsList,
    };

    //“优质供应商”商品推荐
    pageData.recommendSupplierGoods = {
      spmCode: "homepage-tag-goods",
      tagId: res.data?.recommendSupplierGoods?.tagId,
      tagName: authStore.i18n("cm_home.recommendSupplierGoods"),
      goodsList: res.data?.recommendSupplierGoods?.goodsList,
    };

    pageData.bannerConfigs = [
      {
        bannerUrl: backSchoolHotBanner,
        spmCode: "homepage-hot-school",
        tagId: res.data?.h5BackSchoolHotSaleGoods?.tagId,
        goodsList: res.data?.h5BackSchoolHotSaleGoods?.hotSaleGoods,
      },
    ];

    // 定义分类商品配置
    pageData.categoryConfigs = [
      {
        spmCode: "homepage-hot-camera",
        tagId: res.data?.h5CameraHotSaleGoods?.tagId,
        tagName: authStore.i18n("cm_guestHome.camera"),
        goodsList: res.data?.h5CameraHotSaleGoods?.hotSaleGoods,
      },
      {
        spmCode: "homepage-hot-humidifier",
        tagId: res.data?.h5HumidifierHotSaleGoods?.tagId,
        tagName: authStore.i18n("cm_guestHome.humidifier"),
        goodsList: res.data?.h5HumidifierHotSaleGoods?.hotSaleGoods,
      },
    ];

    pageData.habitableCapsuleGoods = {
      spmCode: "homepage-tag-goods",
      tagId: res.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.tagId,
      tagName: authStore.i18n("cm_home.habitableCapsuleGoods"),
      goodsList: res.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.goodsList,
    };
  }
}

const onLinkHover = (index: any) => {
  pageData.activatedLinkIndex = index;
};

function onOpenVideo(video: any, index: any) {
  if (videoModalRef.value) {
    window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
    videoModalRef.value.onOpenVideo(video);
  }
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  padding-bottom: 1.88rem;
  overflow-y: auto;
  background-color: #f6f6f6;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-wrapper {
  display: flex;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  padding: 0.28rem 0 0.36rem 0.26667rem;
  &::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }
  .news-item {
    display: inline-block;
    flex: 0 0 2rem;
    height: 1.28rem;
    border-radius: 0.08rem;
    margin-right: 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0.2rem;
    div {
      width: 1.8rem;
      height: 0.64rem;
      font-size: 0.26rem;
      font-weight: 500;
      color: #fff;
      line-height: 0.4rem;
      white-space: normal;
      display: flex;
      align-items: center;
    }
  }
}

.service-wrapper {
  width: 100%;
  background-color: #fff;
  padding: 0.6rem 0.32rem 0.6rem;
  .service-title {
    font-size: 0.48rem;
    line-height: 0.6rem;
    font-weight: 500;
    color: #222;
  }
  .service-item-wrapper {
    -webkit-flex-direction: column;
    flex-direction: column;
    position: relative;
    width: 100%;
    display: flex;
    .service-item {
      cursor: pointer;
      z-index: 1;
      display: flex;
      .item-title {
        width: fit-content;
        font-size: 0.3rem;
        line-height: 0.3rem;
        padding: 0.12rem 0.28rem 0.12rem 0.24rem;
        color: #333;
        border-radius: 10rem;
        background-color: #fff;
        border: 0.02rem solid #f2f2f2;
      }
    }
    .service-item-enter:not(:last-of-type) {
      margin-bottom: 0.08rem;
    }
    .service-item:not(:last-of-type) {
      margin-bottom: 0.2rem;
    }
    .service-item:not(:first-child) {
      margin-top: 0.2rem;
    }
    .item-circle {
      margin: 0.28rem 0.14rem 0;
      width: 0.12rem;
      height: 0.12rem;
      border: 0.02rem solid #e50113;
      border-radius: 100%;
      flex-shrink: 0;
      position: relative;
      background-color: #fff;
      z-index: 2;
    }
    .service-item-enter .item-circle {
      background-color: #e50113;
    }

    .full-link_item_title {
      font-size: 0.28rem;
      line-height: 0.48rem;
    }
    .item-desc {
      color: #767676;
      display: none;
      font-size: 0.24rem;
      // height: fit-content;
      line-height: 0.24rem;
      margin-top: 0.24rem;
    }
    .service-item-enter .item-title {
      color: #51200b;
      margin-top: -0.1rem;
      color: #fff;
      background-color: #e50113;
      border: 0.02rem solid #e50113;
    }

    .service-item-enter .item-desc {
      display: -webkit-box;
    }
    .item-tail {
      background-color: #f2f2f2;
      height: calc(100% - 0.78rem);
      left: 0.1898rem;
      top: 0.3rem;
      position: absolute;
      width: 0.02rem;
    }
  }
  .service-item-enter:last-of-type ~ .item-tail {
    height: calc(100% - 1.9rem);
  }
}

.full-link {
  width: 100%;
  color: #222;
  background-color: #fff;
  padding: 0.6rem 0.32rem;
  .full-title {
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #222;
    margin-bottom: 0.3rem;
    text-align: center;
  }

  .full-link_title {
    -webkit-box-orient: vertical;
    color: #222;
    display: -webkit-box;
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
  }
  .full-link_item_wrapper {
    -webkit-flex-direction: column;
    flex-direction: column;
    position: relative;
    width: 100%;
    display: flex;
    margin-bottom: 0.32rem;
    .full-link_item {
      cursor: pointer;
      z-index: 1;
      display: flex;
    }
    .full-link_item:not(:last-of-type) {
      margin-bottom: 0.2rem;
    }
    .full-link_item:not(:first-child) {
      margin-top: 0.2rem;
    }
    .full-link_item_title {
      font-size: 0.28rem;
      line-height: 0.48rem;
      font-weight: 500;
    }
    .full-link_item_desc {
      color: #767676;
      font-size: 0.24rem;
      height: fit-content;
      line-height: 0.4rem;
      margin-top: 0.12rem;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .full-link_icon {
      width: 0.4rem;
      height: 0.4rem;
      margin-inline-end: 0.2rem;
      position: relative;
      flex-shrink: 0;
      background-color: #e50113;
      border-radius: 50%;
      margin-top: 0.2rem;
      flex-shrink: 0;
    }
    .full-link_item_tail {
      background-color: #3a180b;
      height: 104%;
      left: 0.18rem;
      position: absolute;
      width: 0.04rem;
      border-radius: 0.04rem;
      top: -0.12rem;
    }
  }
}

.login-guide {
  width: 100%;
  background-color: #473229;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  text-align: center;
  width: 100%;
  position: relative;
  .login-guide-wrapper {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.6rem 0.32rem;
    color: #000;
    z-index: 2;
  }
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1;
  }

  .login-title {
    font-size: 0.4rem;
    font-weight: 500;
    line-height: 0.6rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .login-desc {
    font-size: 0.28rem;
    line-height: 0.4rem;
    margin-top: 0.2rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .section_banner-button {
    width: 3.8rem;
    font-size: 0.32rem;
    line-height: 0.7rem;
    padding: 0.2rem 0.34rem;
    border-radius: 0.4rem;
    margin-top: 0.36rem;
    box-shadow: 0.06rem 0.06rem 0.1rem rgba(0, 0, 0, 0.3);
  }
}

.user-video {
  width: 100%;
  padding: 0.6rem 0.32rem;

  .video-title {
    font-size: 0.36rem;
    font-weight: 500;
    line-height: 0.6rem;
    text-align: center;
  }
  .video-wrapper {
    width: 100%;
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }
  .video-item {
    flex: 0 0 2.8rem;
    width: 2.8rem;
    margin: 0.2rem 0.2rem 0.2rem 0;
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      border-radius: 0.24rem;
    }
    .video-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #fff;
      width: 0.6rem;
      height: 0.6rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
:deep(.n-carousel .n-carousel__slides .n-carousel__slide) {
  text-align: center;
}

:deep(.home-header-carousel.n-carousel .n-carousel__dots) {
  bottom: 0.36rem;
}

.custom-search {
  display: inline-flex;
  padding: 0.34rem 0.48rem;
  align-items: center;
  gap: 0.16rem;
  border-radius: 10rem;
  background: #11263b;
  color: #fff;
  transition: background 0.3s ease;
  &:hover {
    background: #0f2e4d;
  }
}
</style>
