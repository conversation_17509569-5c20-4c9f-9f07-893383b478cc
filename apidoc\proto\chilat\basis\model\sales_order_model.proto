syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "chilat/basis/model/coupon_usable_model.proto";
import "chilat/logistics/logistics_common.proto";
import "chilat/logistics/model/route_model.proto";
import "chilat/trade/model/purchase_model.proto";
import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

//订单列表
message SalesOrderPageListResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated SalesOrderListModel data = 3;
}

// 订单信息
message SalesOrderListModel {
    string id = 1; //订单id
    string orderNo = 2; //订单编号
    string userName = 3; // 用户名
    common.OrderStatus orderStatus = 4; //订单状态
    string orderStatusDesc = 5; //订单状态描述
    int32 goodsQty = 6; //产品总数量
    double goodsPrice = 7; //产品费用
    double commission = 8; //佣金
    int64 cdate = 9; //开单时间
    repeated SoLineModel soLineList = 10; //订单行
    common.PayMode paymentModel = 11; //支付模式: 一次性支付 or 分开支付
    string countryId = 21; //支付国家ID（订单归属国家）
    string countryName = 22; //支付国家名称（订单归属国家）
    string countryNameCn = 23; //支付国家中文名称（订单归属国家）
    common.QuotationMode priceModel = 30; //报价模式
    string userId = 31;
    AmountModel amountModel = 32; //金额, 里面的总金额分线路展示
    double paidAmount = 33; //已支付金额
    string contactName = 40; //收件人
    string phone = 41; //手机号-whatsapp
    string creator = 42; //开单人
    bool isOpenPagSmile = 43; //是否发起pagsmile支付
    string saleManUserName = 44; //业务员用户名
    CouponAmountModel couponAmountModel = 50; //优惠券费用
    int64 payTime = 60; //支付完成时间
    bool isAllPurchased = 70; //是否sku已全部生成了采购单
    bool isNeedOfflinePay = 80; //是否需要线下支付
    common.OrderSourcePlatform sourcePlatform = 160; //订单来源平台
    string sourcePlatformDesc = 170; //订单来源平台描述（前端使用此字段进行显示）
}

//优惠券费用
message CouponAmountModel {
    double productCouponAmount = 1; //优惠券金额
    double commissionCouponAmount = 2; //佣金优惠券金额
}
message AmountModel {
    double goodsPrice = 1; //产品费用
    double commission = 2; //佣金
    double discountAmount = 3; //优惠金额(负数)
    repeated SalesOrderSundryFeeModel sundryFeeList = 4; //杂费信息列表
    repeated SalesOrderRouteModel routeList = 5; //路线列表
    double innerTotalAmount = 6; //国内总金额
}

message SoLineModel {
    string id = 1; //订单行id
    string skuId = 2; // skuid
    string spm = 3; //SPM跟踪码
    int32 goodsQty = 4; //下单数量
    double unitPrice = 5; //商品单价（调整后的单价，公式：sale_price + adjust_price = unit_price）
    double totalPrice = 6; //商品总价格
    string skuImage = 7; //sku图片
    string skuNo = 8; //sku号
    repeated SpecInfoModel specsInfo = 9; //商品规格
    string goodsName = 10; //商品名称
    string goodsPriceUnitName = 11; //价格单位
    string goodsId = 12; //商品id
    string goodsNo = 13; //商品编码
    string skuKey = 86; //SKU列表中的sku唯一键（由“skuId+padc”组合而成）
    string padc = 88; //促销活动动态代码
    string paName = 89; //促销活动名称（活动显示名称）
}

//订单行信息
message SalesOrderLineModel {
    string id = 10; //订单行id
    string skuId = 20; // sku id
    string spm = 30; //SPM跟踪码
    int32 goodsQty = 40; //下单数量
    double salePrice = 51; //商品销售价（阶梯价）
    double totalSalePrice = 52; //商品销售价总价格（公式：sales_price * goods_qty = total_sale_price）
    double adjustPrice = 53; //商品单价调整金额（正数表示涨价，负数表示减价）
    double unitPrice = 54; //商品单价
    double totalPrice = 60; //商品总价格
    double actualUnitPrice = 70; //实际单价（扣除优惠后分摊的单价）
    double totalActualPrice = 80; //商品总价格（商品实际总价格）
    string skuImage = 90; //sku图片
    string skuNo = 100; //sku号
    repeated SpecInfoModel specsInfo = 110; //商品规格
    string goodsName = 120; //商品名称
    string goodsPriceUnitName = 130; //价格单位
    string goodsPriceUnitNameCN = 135; //中文价格单位
    int32 purchaseSendNum = 140; //采购发货数量
    repeated SalesOrderLineLookingModel lineLookingList = 150; //询盘单订单行绑定信息
    string goodsNo = 160; //商品编码
    string goodsId = 170; //商品id
    double supplierPrice = 180; //采购价（单价）
    double currentSupplierPrice = 190; //当前采购价（查询最新商品信息获取的采购价）
    int32 seqNo = 200; //订单商品行顺序号（从1开始）
    string orderLineNo = 210; //订单商品行号
    bool isGoodsActive = 220; //商品SKU是否为有效状态（开单预览接口返回）
    bool isGoodsOnline = 230; //商品SKU是否为上架状态（开单预览接口返回）
    string sourceShopId = 240;
    string sourceGoodsId = 250;
    string sourceSkuId = 260;
    common.OrderStatus orderStatus = 270; //订单行状态
    string orderStatusDesc = 271; //订单行状态描述
    string goodsNameCn = 280; //商品中文名称
    int32 goodsSource = 300; //商品来源
    bool isMainImage = 301; //sku图片是否主图
    string skuKey = 86; //SKU列表中的sku唯一键（由“skuNo+padc”组合而成）
    int32 padcDataType = 87; //padc关联的数据类型
    string padc = 88; //促销活动动态代码
    string paName = 89; //促销活动名称（活动显示名称）
    int32 boxInsideCount = 310; //装箱数
    double boxUnitWeight = 320; //单箱重量
    double boxUnitVolume = 330; //单箱体积
}

//询盘行信息
message SalesOrderLineLookingModel {
    string id = 1; //id
    int32 goodsQty = 2; //商品总数
    string goodsLookingNo = 3; //询盘单号
    string lookingGoodsLineId = 4; //询盘单商品行id
//    bool deleted = 5; //是否删除
}

//装箱信息
message SalesOrderBoxModel {
    string id = 1; // 装箱id
    string spm = 2; //SPM跟踪码
    int32 goodsQty = 3; //商品总数
    int32 boxInsideCount = 4; //装箱数
    double boxQty = 5; // 箱数
    double boxUnitWeight = 6; // 单件重量
    double totalWeight = 7; //总重量
    double boxUnitVolume = 8; //单箱体积
    double totalVolume = 9; //总体积
    repeated SalesOrderBoxLineModel boxLineList = 10; //装箱明细
    repeated SalesOrderBoxRouteFeeModel boxRouteFeeList = 11; //运输费
//    bool deleted = 13; //是否删除（修改用）
}

//装箱行
message SalesOrderBoxLineModel {
    string id = 1; // 装箱行id
    string skuId = 2; //SKU ID
    int32 goodsQty = 3; //商品总数
    double salePrice = 19; //商品销售价（原价）
    double unitPrice = 4; //商品单价（结算单价）
    double totalPrice = 5; //商品总价格（结算总价）
    string skuImage = 6; //sku图片
    string skuNo = 7; //sku号
    repeated SpecInfoModel specInfo = 8; //商品规格
//    bool deleted = 9; //是否删除（修改用）
    string goodsNo = 10; //商品编码
    string goodsId = 11; //商品id
    string goodsName = 12; //商品名称
    string goodsPriceUnitName = 13; //价格单位
    string sourceShopId = 14; //源店铺id
    string sourceGoodsId = 15; //源商品id
    string goodsPriceUnitNameCn = 16; //商品计价单位名称中文
    double supplierPrice = 17;// 采购价（下单时的采购价）
    double currentSupplierPrice = 18; //当前采购价（查询最新商品信息获取的采购价）
    string skuKey = 86; //SKU列表中的sku唯一键（由“skuNo+padc”组合而成）
    int32 padcDataType = 87; //padc关联的数据类型
    string padc = 88; //促销活动动态代码
    string paName = 89; //促销活动名称（活动显示名称）
}

message SalesOrderBoxRouteFeeModel {
    string id = 1; //id
    string routeId = 2; //线路id
    string routeName = 3; //线路名称
    string routeAlias = 4; //线路别名
    double transportFee = 5; //单箱运费
    double totalTransportFee = 6; //线路总运费(单箱运费*箱数)
//    bool deleted = 8; //是否删除（修改用）
}


//订单杂费
message SalesOrderSundryFeeModel {
    string id = 1; //id
    string feeId = 2; //费用ID
    string feeName = 3; //费用名称
    string feeAlias = 4; // 费用明细类型： EXW CIF
    double totalFee = 5; //总费用
    common.FeeType feeType = 6; //国内费用 or 国外费用
//    repeated SalesOrderSundryFeeLineModel soFeeLineList = 7; //费用明细列表
}

//订单杂费明细（二级费用）
message SalesOrderSundryFeeLineModel {
    string id = 1; //id
    string feeAlias = 2; // 费用明细类型： EXW CIF
    string feeItemId = 3; //费用项id
    string feeItemName = 4; //费用项名称
    double fee = 5;// 费用
    repeated SalesOrderSundryFeeLineModel soFeeLineList = 6; //明细
//    bool deleted = 7; //是否删除
}

//订单账单
message SalesOrderBillModel {
    string id = 1; //账单号
    common.SalesOrderBillType billType = 2; //账单类型：100.国内费用 200.国际费用
    common.SalesOrderBillStatus billStatus = 3; //账单状态： -100.取消 0.待支付 100.部分支付 200.已支付
    string billStatusDesc = 4; //账单状态描述
    string remark = 5; //账单备注
    int64 finishTime = 6; //完成时间
    double billAmount = 7; //金额
    double billPaidAmount = 8; //已付金额
}

//销售单线路信息与线路总运费
message SalesOrderRouteModel {
    string id = 1;
    string routeId = 2; //线路id
    string routeName = 3; //线路名称
    string routeCode = 4; //线路code
    string routeAlias = 5; //线路别名
    string remark = 6; //备注
    logistics.RouteTransportDaysModel transportDays = 7; //交期
    double totalTransportFee = 20; //线路总运费
}

//支付流水
message SalesOrderBillPaymentModel {
    string id = 1; //账单号
    double paymentAmount = 2; //支付金额
    string paymentType = 3; //支付方式
    repeated string billId = 4; //账单号
    string remark = 5; //备注备注
    int64 cdate = 6; //支付时间
    string coperator = 7; //操作人
    repeated string payOrderPicList = 8; //支付凭证url列表
}

//订单日志
message SalesOrderLogModel {
    common.OrderStatus status = 1; //状态
    common.SalesOrderOperationType operateType = 2; //操作类型
    string remark = 5; //操作内容
    string operateName = 6; //操作人
    string cdate = 7; //操作时间

}

//订单详细信息
message SalesOrderDetailModel {
    string userId = 1; //用户id
    string userName = 2; // 用户名
    string orderNo = 3; //订单号
    common.OrderStatus orderStatus = 4; //订单状态
    int64 cdate = 5; //开单时间
//    string country = 6; //国家
    repeated SalesOrderRouteModel routeModels = 7; //路线信息
    string routeDesc = 8; //线路描述（运输方式）弃用
    repeated string goodsLookingNoList = 9; //关联询盘单号
    string buyerRemark = 10; //客户备注
    common.QuotationMode priceModel = 11; //报价模式
    common.PayMode paymentModel = 12; //支付模式:0.一次性支付 100.首次支付只支付国内费用，二次支付待仓库收货之后再支付
    string paymentModelDesc = 13; //支付模式描述
    OrderAddressModel orderAddress = 14; //配送信息
    repeated SalesOrderBoxModel salesOrderBoxList= 15; //装箱信息
    repeated SalesOrderSundryFeeModel salesOrderFeeList = 16; //杂费信息（除了产品价值，佣金，运费等主费用之外的费用，如国内拖车等）
    repeated SalesOrderBillModel soBillList = 17; //账单信息
    repeated SalesOrderBillPaymentModel soBillPaymentList = 18; //支付流水
    repeated SalesOrderLogModel soLogList = 19; //订单日志
    repeated SalesOrderLineModel soLineList = 20; //订单行信息
    double commission = 21; //佣金
    double goodsPrice = 22; //产品总金额
    int32 skuCount = 23; //sku总数
    int32 goodsCount = 24; //goods总数（SPU数）
    int32 totalGoodsQty = 25; //商品总件数
    string countryId = 31; //支付国家ID（订单归属国家）
    string countryName = 32; //支付国家名称（订单归属国家）
    string countryNameCn = 33; //支付国家中文名称（订单归属国家）
    bool isRouteFeeInBox = 40; //装箱信息中，是否包含线路运费（必填）
    double goodsDiscountAmount = 50;//商品优惠金额（大于0，表示享受了商品优惠）
    double goodsActualPrice = 60;//实际商品总金额（减优惠）
    string sellerRemark = 70; //卖家备注
    double routeFee = 80; //线路费用
    double innerSundryFee = 85; //国内段杂费
    double domesticFee = 90; //国内费用
    double outerSundryFee = 95; //国际段杂费
    double internationalFee = 100; //国际费用
    double totalActualPrice = 110; //订单总金额(减优惠)
    double goodsSalePrice = 115; //商品销售价总价格（平衡公式：goods_sales_price + goods_adjust_price = goods_price）
    double goodsAdjustPrice = 116; //商品调整金额合计（正数表示涨价，负数表示减价；包含业务员或客服改价调整的金额）
    double paidAmount = 120; //已支付金额
    repeated PagSmilePayInfo pagSmilePayInfoList = 130; //pagsmile支付信息
    CouponAmountModel couponAmountModel = 140; //优惠券费用
    string orderStatusDesc = 150; //订单状态描述
    common.OrderSourcePlatform sourcePlatform = 160; //订单来源平台
    string sourcePlatformDesc = 170; //订单来源平台描述（前端使用此字段进行显示）
    bool supportOnlinePay = 180; //是否支持在线支付（默认true）
}

message PagSmilePayInfo {
    string transactionId = 10; //pagsmile侧的transactionId
    string merchantTradeNo = 20;//应用侧的tradeNo
    string pagSmileTradeStatus = 30; //pagmsile侧的交易状态
    int64 pagSmileCreateTime = 40; //pagsmile侧的创建时间, 毫秒粒度时间戳
    string payMethod = 50; //pagsmile pay method
    string payMethodChannel = 60; //pagsmile method channel
    string payAmount = 70; //pagsmile (币制 + 金额)
    string payUrl = 80; // pagsmile支付链接
}

// 订单地址信息
message OrderAddressModel {
    string id = 1;
    string phone = 2; //手机号（包含区号）
    string email = 3; //邮箱
    string countryName = 4; //国家名称
    string countryId = 5; //国家id
    string provinceCode = 6; //省code
    string province = 7; //省
    string cityCode = 8; //市code
    string city = 9; //市/县
    string regionCode = 10; //区县code
    string region = 11; //区域
    string postcode = 12; //邮政编码
    string address = 13; //详细地址
    string houseNo = 14; //房好
    string referLandmark = 15; //参考地标
    string contactName = 16; // 联系人
    common.AddressLabel addressLabel = 17; //地址标签：10：家，20公司
    string street = 18; //街道地址
    string addressId = 19; //用户地址id
    string fullAddress = 20; //完整的地址描述
}

//订单详情返回
message SalesOrderDetailResp {
    common.Result result = 1;
    SalesOrderDetailModel data = 2;
}

// 创单返回
message OrderCreateModel {
   string orderNo = 1; //订单号
}

// 创单返回
message SalesOrderCreateResp {
    common.Result result = 1;
    OrderCreateModel data = 2;
}


// 订单信息
message SalesOrderPayModel {
    string userId = 1; //用户id
    string userName = 2; // 用户名
    string orderNo = 3; //订单号
    int32 orderStatus = 4; //订单状态
    int64 cdate = 5; //开单时间
//    string country = 6; //国家
    common.QuotationMode priceModel = 8; //报价模式
    common.PayMode paymentModel = 9; //支付模式:0.一次性支付 100.首次支付只支付国内费用，二次支付待仓库收货之后再支付
    repeated SalesOrderBillModel soBillList = 10; //账单信息
    double commission = 11; //佣金
    double goodsPrice = 12; //产品总金额
    repeated SalesOrderRouteModel routeList = 13; //线路
    int32 totalGoodsQty = 14; //商品总件数
    string countryId = 21; //国家ID
    string countryName = 22; //国家名称
    string countryNameCn = 23; //国家中文名称
    repeated SalesOrderSundryFeeModel salesOrderSundryFeeList = 30; //杂费信息（除了产品价值，佣金，运费等主费用之外的费用，如国内拖车等）
    double goodsDiscountAmount = 40;//商品优惠金额（大于0，表示享受了商品优惠）
    double goodsActualPrice = 50;//实际商品总金额（减优惠）
    double productCouponActualPrice = 51;//产品优惠券金额
    double commissCouponActualPrice = 52;//佣金优惠券金额

}

//线路
message RouteModel {
    SalesOrderRouteModel soRoute = 1; //线路id
    double transPortFee = 2; //运费
}
//订单支付查询
message SalesOrderPayResp {
    common.Result result = 1;
    SalesOrderPayModel data = 2;
}

message SpecInfoModel {
    string itemId = 1;
    string itemName = 2;
    string itemNameCn = 3;
    string specId = 4;
    string specName = 5;
    string specNameCn = 6;
}

message QueryOffLinePayInfoResp {
    common.Result result = 1;
    OffLinePayInfoModel data= 2;
}

message InternationalAmountModel {
    SalesOrderRouteModel routeModel = 10; //线路信息
    double totalAmount = 20; //国际账单总费用：国际运费加上国际杂费
    double unPaidAmount = 30; //国际账单未支付费用
}

message OffLinePayInfoModel {
    double innerBillAmount = 10;  //国内账单金额
    double innerUnPaidAmount = 20; //国内账单待支付金额
    repeated InternationalAmountModel internationalAmountList = 30; //线路及其对应的国际账单金额列表,切换线路时,总金额和待支付金额需要跟着线路变化
    string amountRemark = 40; //需要支付的账单总金额的备注说明
    common.PayMode payMode = 60; //一次性支付 or 分开支付, 如果为分开支付，isOnlyInternational=false时, 总金额为国内账单金额
    double diffAmount = 70; //分开支付且当前支付国际费用，如果是diffAmount > 0, 说明是国内费用之前存在不足的部分，剩余金额留到和国际费用一起支付； 反之，说明国内费用之前多付了，多付的部分留到国际费用抵扣
}

//开单页面选项返回
message SalesOrderPageOptionResp {
    common.Result result = 1;
    SalesOrderPageOptionModel data = 2;
}

//开单页面选项
message SalesOrderPageOptionModel {
    repeated SalesOrderSundryFeeOptionModel sundryFeeOptions = 10; //开单费用选项
}

//开单费用选项
message SalesOrderSundryFeeOptionModel {
    string feeId = 10; //费用ID
    string feeName = 20; //费用名称
    string feeAlias = 30; //费用别名
    logistics.FeeStage feeStage = 40; //收费阶段（分为“国内段”与“国际段”）
//TODO: 后续增加
//    repeated string routeIds = 6; //适用线路（空值，表示不限线路）
//    repeated SalesOrderSundryFeeOptionModel children = 9; //子费用项
}

//商城订单详情预览链接返回
message MallOrderDetailPreviewResp {
    common.Result result = 1;
    MallOrderDetailPreviewModel data = 2;
}

//商城订单详情预览链接
message MallOrderDetailPreviewModel {
    string url = 10;
}

message GetSalesManListResp {
    common.Result result = 10;
    SalesManListModel data = 20;
}

message SalesManListModel {
    repeated UserBaseInfoModel userList = 1;
}

message UserBaseInfoModel {
    string userId = 1;
    string userName = 2;
}

message PurchaseDataBySalesOrderResp {
    common.Result result = 1;
    PurchaseDataBySalesOrderModel data = 2;
}

message PurchaseDataBySalesOrderModel {
    repeated trade.PurchaseOrderSkuModel skuList = 1;
    bool isAllPurchased = 2; //是否sku已全部生成了采购单
    string salesManId = 3; // 业务员ID
    string salesManName = 4; // 业务员名称
}

message OrderLineResp {
    common.Result result = 1;
    OrderLineModel data = 2;
}

message OrderBasicModel {
    string orderNo = 1;
    string salesManId = 2; // 业务员ID
    string salesManName = 3; // 业务员名称
}

message OrderLineModel {
    repeated SalesOrderLineModel soLineList = 1;
    OrderBasicModel orderBasic = 2; // 订单基础信息
}

message GetOrderStatusListResp {
    common.Result result = 1;
    OrderStatusListModel data = 2;
}

message OrderStatusListModel {

    repeated OrderStatusModel orderStatusList = 1;
}

message OrderStatusModel {
    common.OrderStatus orderStatus = 1; //订单状态
    string orderStatusDesc = 2; //订单状态描述
}
