<template>
  <div class="mobile-container">
    <div class="page-header">
      <video
        autoplay
        loop
        muted
        playsinline
        :poster="mobileNotasBg"
        :src="configStore.pageTheme.mobileNotasVideo"
        class="absolute top-0 left-0 w-full h-full object-cover z-0"
      ></video>
      <div
        class="absolute top-0 left-0 w-full h-full bg-black opacity-65 z-1"
      ></div>
      <div class="relative z-1">
        <div class="w-[2.44rem] h-[0.56rem]">
          <img
            loading="lazy"
            :src="configStore.pageTheme.chilatWhiteLogo"
            alt="logo"
            class="w-[2.44rem]"
          />
        </div>

        <div class="text-[0.64rem] leading-[0.8rem] mt-[1.2rem] pr-[0.6rem]">
          {{ authStore.i18n("cm_nota.simplifyImport") }}
        </div>
        <div class="text-[0.36rem] leading-[0.56rem] mt-[0.8rem]">
          {{ authStore.i18n("cm_nota.easyImport") }}
        </div>
        <div class="mt-[0.4rem] text-[0.32rem] leading-[0.6rem]">
          <n-space vertical :style="{ gap: '0.04rem 0' }">
            <div>
              <icon-card
                name="lets-icons:check-round-fill"
                size="26"
                color="#fff"
                class="mr-[0.2rem]"
              >
              </icon-card>
              {{ authStore.i18n("cm_nota.stepOneTitle") }}
            </div>
            <div>
              <icon-card
                name="lets-icons:check-round-fill"
                size="26"
                color="#fff"
                class="mr-[0.2rem]"
              >
              </icon-card>
              {{ authStore.i18n("cm_nota.stepTwoTitle") }}
            </div>
            <div>
              <icon-card
                name="lets-icons:check-round-fill"
                size="26"
                color="#fff"
                class="mr-[0.2rem]"
              >
              </icon-card>
              {{ authStore.i18n("cm_nota.stepThreeTitle") }}
            </div>
            <div>
              <icon-card
                name="lets-icons:check-round-fill"
                size="26"
                color="#fff"
                class="mr-[0.2rem]"
              >
              </icon-card>
              {{ authStore.i18n("cm_nota.stepFourTitle") }}
            </div>
          </n-space>
        </div>
        <div class="text-[0.32rem] mt-[0.6rem]">
          {{ authStore.i18n("cm_nota.preferredSelection") }}
        </div>
      </div>
    </div>
    <div class="pb-[0.2rem] mt-[0.12rem] page-form text-center" id="pageForm">
      <n-button
        color="#E50113"
        @click="onHandleWhatsAppClick(1)"
        data-spm-box="potential_user_note_submit"
        class="w-[6.6rem] mx-auto py-[0.4rem] rounded-[4rem] text-[0.36rem] leading-[0.36rem] font-medium"
      >
        <div
          class="flex items-center justify-center"
          v-html="authStore.i18n('cm_nota.submitWhatsapp')"
        ></div>
      </n-button>
    </div>
    <!-- <div class="page-form w-full" id="pageForm">
      <n-form
        :rules="userRules"
        :model="userForm"
        ref="userFormRef"
        label-align="left"
        label-placement="top"
        class="mt-[0.2rem]"
      >
        <div
          class="px-[0.2rem] border border-[#E6E6E6] rounded-[0.24rem] overflow-hidden"
        >
          <n-form-item
            path="contactName"
            class="top-form-item input-form-item"
            :label="authStore.i18n('cm_nota.username')"
          >
            <n-input
              v-trim
              clearable
              maxlength="100"
              class="custom-input"
              @keydown.enter.prevent
              v-model:value="userForm.contactName"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.contactName,
                  authStore.i18n('cm_nota.username')
                )
              "
            />
          </n-form-item>
          <n-form-item
            path="countryId"
            class="top-form-item input-form-item"
            :label="authStore.i18n('cm_nota.country')"
          >
            <n-select
              filterable
              value-field="id"
              label-field="countryEsName"
              class="custom-input"
              :options="pageData.countryList"
              v-model:value="userForm.countryId"
              @update:value="
                (value, option) =>
                  onSelectCountry(
                    value,
                    option,
                    authStore.i18n('cm_nota.country')
                  )
              "
              :placeholder="authStore.i18n('cm_nota.selectPlaceholder')"
            />
          </n-form-item>
          <n-form-item
            path="whatsapp"
            class="others-form-item input-form-item"
            :label="authStore.i18n('cm_nota.whatsapp')"
          >
            <div class="!w-[0.8rem] ml-[0.06rem]">
              <span v-if="pageData?.countryRegexps?.areaCode">{{
                pageData.countryRegexps.areaCode
              }}</span>
              <span class="text-[#A6A6A6]" v-else>+000</span>
            </div>
            <n-divider vertical class="h-full" />
            <n-input
              v-trim
              clearable
              maxlength="64"
              class="custom-input"
              @keydown.enter.prevent
              v-model:value="userForm.whatsapp"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.whatsapp,
                  authStore.i18n('cm_nota.whatsapp')
                )
              "
            />
          </n-form-item>
          <n-form-item
            path="email"
            class="top-form-item input-form-item"
            :label="authStore.i18n('cm_nota.email')"
          >
            <n-input
              v-trim
              clearable
              maxlength="64"
              class="custom-input"
              @keydown.enter.prevent
              v-model:value="userForm.email"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(userForm.email, authStore.i18n('cm_nota.email'))
              "
            />
          </n-form-item>
          <n-form-item
            path="userType"
            :label="authStore.i18n('cm_nota.userType')"
            class="top-form-item"
          >
            <n-radio-group
              class="mb-[0.08rem]"
              v-model:value="userForm.userType"
              :on-update:value="
                (value) =>
                  onSelectEvent(
                    value,
                    'userType',
                    authStore.i18n('cm_nota.userType')
                  )
              "
            >
              <n-space vertical>
                <n-radio
                  v-for="item in userTypeList"
                  :value="item.value"
                  :key="item.value"
                >
                  <div class="flex">
                    <div>{{ item.label }}</div>
                  </div>
                </n-radio>
              </n-space>
              <n-form-item
                label=""
                path="userTypeRemark"
                class="inner-form-item"
                v-if="userForm.userType === 'POTENTIAL_USER_TYPE_OTHER'"
              >
                <n-input
                  round
                  v-trim
                  clearable
                  maxlength="200"
                  @keydown.enter.prevent
                  v-model:value="userForm.userTypeRemark"
                  :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                />
              </n-form-item>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            path="withImportExperience"
            class="top-form-item input-form-item"
            :label="authStore.i18n('cm_nota.withImportExperience')"
          >
            <n-radio-group
              v-model:value="userForm.withImportExperience"
              :on-update:value="
                (value) =>
                  onSelectEvent(
                    value,
                    'withImportExperience',
                    authStore.i18n('cm_nota.withImportExperience')
                  )
              "
            >
              <n-space>
                <n-radio
                  v-for="item in withImportExperienceList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</n-radio
                >
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            path="serviceType"
            class="top-form-item"
            :label="authStore.i18n('cm_nota.serviceType')"
          >
            <n-radio-group
              class="mt-[0.08rem]"
              v-model:value="userForm.serviceType"
              :on-update:value="
                (value) =>
                  onSelectEvent(
                    value,
                    'serviceType',
                    authStore.i18n('cm_nota.serviceType')
                  )
              "
            >
              <n-space
                vertical
                :style="{ gap: '0.18rem 0' }"
                class="mb-[0.1rem]"
              >
                <n-radio
                  v-for="item in serviceTypeList"
                  :value="item.value"
                  :key="item.value"
                >
                  {{ item.label }}
                  <ul>
                    <li
                      v-for="desc in item.labelDesc"
                      :key="desc"
                      v-html="highlightUSPrice(desc)"
                    ></li>
                  </ul>
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            path="buyerRemark"
            :label="authStore.i18n('cm_nota.buyerRemark')"
            :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
            class="border-b-1 border-[#e6e6e6] pt-[0.28rem] !pb-[-0.2rem] mt-[0.16rem] rounded-[0.08rem]"
          >
            <n-input
              v-trim
              clearable
              type="textarea"
              :maxlength="60000"
              @keydown.enter.prevent
              class="rounded-[0.4rem]"
              v-model:value="userForm.buyerRemark"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.buyerRemark,
                  authStore.i18n('cm_nota.buyerRemark')
                )
              "
            />
          </n-form-item>
          <n-form-item
            path="captchaCode"
            class="mt-[0.4rem]"
            :label="authStore.i18n('cm_nota.captchaCode')"
            :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
          >
            <n-image
              class="mr-[0.16rem]"
              :preview-disabled="true"
              :src="pageData.captchaImage"
              @click="onRefreshCaptcha"
            ></n-image>
            <n-input
              v-trim
              clearable
              :maxlength="100"
              class="rounded-[0.16rem]"
              @keydown.enter.prevent
              v-model:value="userForm.captchaCode"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              @blur="
                onBlurEvent(
                  userForm.captchaCode,
                  authStore.i18n('cm_nota.captchaCode'),
                  'captchaCode'
                )
              "
            />
          </n-form-item>
          <div class="pb-[0.2rem] mt-[0.12rem]">
            <n-button
              color="#E50113"
              @click="onSubmit"
              :loading="pageData.submitLoading"
              data-spm-box="potential_user_note_submit"
              class="w-full py-[0.4rem] rounded-[4rem] text-[0.36rem] leading-[0.36rem] font-medium"
            >
              <div
                class="flex items-center justify-center"
                v-html="authStore.i18n('cm_nota.onlineSubmit')"
              ></div>
            </n-button>
          </div>
        </div>
      </n-form>
    </div> -->
    <div class="w-full text-[#333]">
      <div class="text-[0.68rem] leading-[0.76rem] px-[0.4rem] pt-[0.8rem]">
        <div>
          {{ authStore.i18n("cm_nota.provideSolutions") }}
        </div>
        <img
          loading="lazy"
          src="@/assets/icons/open/arrow.svg"
          alt="down"
          class="w-[0.48rem] mt-[0.6rem] mx-auto"
        />
      </div>
      <div class="w-full mt-[0.68rem]">
        <n-grid x-gap="12" y-gap="20" :cols="2" class="px-[0.4rem]"
          ><n-grid-item
            v-for="(item, index) in companyData"
            :key="index"
            class="number-item"
            @mouseover="handleMouseOver(index)"
            :class="
              pageData.activatedIndex === index ? 'activated-number-item' : ''
            "
            ><span class="text-[0.72rem] leading-[0.72rem]">{{
              item.number
            }}</span>
            <div
              class="w-[2.56rem] text-[0.24rem] leading-[0.24rem] mt-[0.04rem]"
            >
              {{ item.numberDesc }}
            </div></n-grid-item
          >
        </n-grid>
        <n-space
          :style="{ gap: '0 0.04rem' }"
          class="px-[0.16rem] mt-[0.72rem]"
        >
          <img
            loading="lazy"
            alt="img"
            class="max-w-[3rem] h-[1.96rem]"
            v-for="(imageUrl, index) in companyData[pageData.activatedIndex]
              .imgData"
            :key="index"
            :src="imageUrl"
          />
        </n-space>
        <div class="text-center mt-[0.6rem]">
          <n-button
            color="#E50113"
            @click="onHandleWhatsAppClick(2)"
            data-spm-box="potential_user_note_submit"
            class="w-[6.6rem] mx-auto py-[0.4rem] rounded-[4rem] text-[0.36rem] leading-[0.36rem] font-medium"
          >
            <div
              class="flex items-center justify-center"
              v-html="authStore.i18n('cm_nota.submitWhatsapp')"
            ></div>
          </n-button>
          <!-- <n-button
            color="#e50113"
            class="px-[1rem] py-[0.4rem]"
            @click="onScrollToNav(1)"
          >
            {{ authStore.i18n("cm_nota.startNow") }}
          </n-button> -->
        </div>
      </div>
      <div class="w-full mt-[0.8rem]">
        <div class="mt-[0.6rem] px-[0.4rem]">
          <img
            loading="lazy"
            src="@/assets/icons/open/arrow.svg"
            alt="down"
            class="w-[0.48rem] mx-auto"
          />
          <div
            class="text-[0.44rem] leading-[0.52rem] font-medium text-center mt-[0.36rem]"
          >
            <div>{{ authStore.i18n("cm_nota.stepOne") }}</div>
            <div>{{ authStore.i18n("cm_nota.stepOneTitle") }}</div>
          </div>
          <div
            class="w-[3rem] mx-auto text-[0.28rem] leading-[0.28rem] italic text-[#e50113] py-[0.2rem] border border-[#D9D9D9] text-center mt-[0.4rem]"
          >
            {{ authStore.i18n("cm_nota.chooseOneOfThree") }}
          </div>
          <n-space vertical :style="{ gap: '0.24rem 0' }" class="mt-[0.6rem]">
            <div
              v-for="(item, index) in serviceData"
              :key="index"
              class="relative w-full bg-white border border-[#333] rounded-[0.4rem] pt-[3.72rem] px-[0.24rem] pb-[0.44rem]"
            >
              <img
                loading="lazy"
                :src="item.imgUrl"
                alt="visitChina"
                class="w-full absolute top-0 left-0"
              />
              <div class="text-[0.36rem] leading-[0.44rem] font-medium">
                {{ item.title }}
              </div>
              <n-space
                vertical
                :style="{ gap: '0.16rem 0' }"
                class="text-[0.28rem] leading-[0.36rem] mt-[0.2rem]"
              >
                <div v-for="desc in item.descData" :key="desc">
                  <span v-html="newHighlightUSPrice(desc)"></span>
                </div>
              </n-space>
            </div>
          </n-space>
          <div class="text-center mt-[0.6rem]">
            <n-button
              color="#E50113"
              @click="onHandleWhatsAppClick(3)"
              data-spm-box="potential_user_note_submit"
              class="w-[6.6rem] mx-auto py-[0.4rem] rounded-[4rem] text-[0.36rem] leading-[0.36rem] font-medium"
            >
              <div
                class="flex items-center justify-center"
                v-html="authStore.i18n('cm_nota.submitWhatsapp')"
              ></div>
            </n-button>
            <!-- <n-button
              color="#e50113"
              class="px-[1rem] py-[0.4rem]"
              @click="onScrollToNav(2)"
            >
              {{ authStore.i18n("cm_nota.startHere") }}
            </n-button> -->
          </div>
        </div>
        <div class="mt-[0.6rem] px-[0.4rem]">
          <img
            loading="lazy"
            src="@/assets/icons/open/arrow.svg"
            alt="down"
            class="w-[0.48rem] mx-auto"
          />
          <div
            class="text-[0.44rem] leading-[0.52rem] font-medium text-center mt-[0.36rem]"
          >
            <div>{{ authStore.i18n("cm_nota.stepTwo") }}</div>
            <div>{{ authStore.i18n("cm_nota.stepTwoTitle") }}</div>
          </div>
          <div
            class="w-[3rem] mx-auto text-[0.28rem] leading-[0.28rem] italic text-[#e50113] py-[0.2rem] border border-[#D9D9D9] text-center mt-[0.4rem]"
          >
            {{ authStore.i18n("cm_nota.chooseOneOfTwo") }}
          </div>
          <n-space
            vertical
            :style="{ gap: '0.24rem 0' }"
            class="mt-[0.6rem] text-center"
          >
            <div
              class="relative w-full bg-white border border-[#333] rounded-[0.4rem] pt-[4rem] px-[0.24rem] pb-[0.68rem]"
            >
              <img
                loading="lazy"
                :src="mobileSelfShipping"
                alt="visitChina"
                class="w-full absolute top-0 left-0"
              />
              <div class="text-[0.36rem] leading-[0.44rem] font-medium">
                {{ authStore.i18n("cm_nota.arrangeTransport") }}
              </div>
            </div>
            <div
              class="relative w-full bg-white border border-[#333] rounded-[0.4rem] pt-[4rem] px-[0.24rem] pb-[0.68rem]"
            >
              <img
                loading="lazy"
                :src="mobileDoorToDoor"
                alt="visitChina"
                class="w-full absolute top-0 left-0"
              />
              <div class="text-[0.44rem] leading-[0.66rem] font-medium">
                {{ authStore.i18n("cm_nota.doorToDoor") }}
              </div>
              <div class="mt-[0.2rem] text-[0.28rem] leading-[0.36rem]">
                {{ authStore.i18n("cm_nota.logisticsCompany") }}
              </div>
            </div>
          </n-space>
          <div class="text-center mt-[0.6rem]">
            <n-button
              color="#E50113"
              @click="onHandleWhatsAppClick(4)"
              data-spm-box="potential_user_note_submit"
              class="w-[6.6rem] mx-auto py-[0.4rem] rounded-[4rem] text-[0.36rem] leading-[0.36rem] font-medium"
            >
              <div
                class="flex items-center justify-center"
                v-html="authStore.i18n('cm_nota.submitWhatsapp')"
              ></div>
            </n-button>
            <!-- <n-button
              color="#e50113"
              class="px-[1rem] py-[0.4rem]"
              @click="onScrollToNav(3)"
            >
              {{ authStore.i18n("cm_nota.startHere") }}
            </n-button> -->
          </div>
        </div>
        <div class="mt-[0.6rem]">
          <img
            loading="lazy"
            src="@/assets/icons/open/arrow.svg"
            alt="down"
            class="w-[0.48rem] mx-auto"
          />
          <div
            class="text-[0.44rem] leading-[0.52rem] font-medium text-center mt-[0.36rem]"
          >
            <div>{{ authStore.i18n("cm_nota.stepThree") }}</div>
            <div>{{ authStore.i18n("cm_nota.stepThreeTitle") }}</div>
          </div>
          <div
            class="w-[2.56rem] mx-auto text-[0.28rem] leading-[0.28rem] italic text-[#e50113] py-[0.2rem] text-center mt-[0.4rem] rounded-[0.08rem] bg-[#fff] relative z-10 border border-[#e50113]"
          >
            {{ authStore.i18n("cm_nota.ourService") }}
          </div>
          <n-space
            vertical
            :style="{ gap: '0.24rem 0' }"
            class="w-full py-[0.96rem] flex justify-between text-center bg-[#F2F2F2] px-[0.4rem] rounded-[0.08rem] mt-[-0.36rem] text-center"
          >
            <div
              class="relative w-full bg-white border border-[#333] rounded-[0.4rem] pt-[4.24rem] px-[0.24rem] pb-[0.66rem]"
            >
              <img
                loading="lazy"
                :src="mobilePlaceOrder"
                alt="visitChina"
                class="w-full absolute top-0 left-0"
              />
              <div class="text-[0.36rem] leading-[0.44rem] font-medium">
                {{ authStore.i18n("cm_nota.retrieveOrders") }}
              </div>
            </div>
            <div
              class="relative w-full bg-white border border-[#333] rounded-[0.4rem] pt-[4.24rem] px-[0.24rem] pb-[0.66rem]"
            >
              <img
                loading="lazy"
                :src="mobileReceiveQuality"
                alt="visitChina"
                class="w-full absolute top-0 left-0"
              />
              <div class="text-[0.36rem] leading-[0.44rem] font-medium">
                {{ authStore.i18n("cm_nota.goodsReception") }}
              </div>
            </div>
            <div class="text-center mt-[0.6rem]">
              <n-button
                color="#E50113"
                @click="onHandleWhatsAppClick(5)"
                data-spm-box="potential_user_note_submit"
                class="w-[6.6rem] mx-auto py-[0.4rem] rounded-[4rem] text-[0.36rem] leading-[0.36rem] font-medium"
              >
                <div
                  class="flex items-center justify-center"
                  v-html="authStore.i18n('cm_nota.submitWhatsapp')"
                ></div>
              </n-button>
              <!-- <n-button
                color="#e50113"
                class="px-[1rem] py-[0.4rem]"
                @click="onScrollToNav(4)"
              >
                {{ authStore.i18n("cm_nota.startHere") }}
              </n-button> -->
            </div>
          </n-space>
        </div>
        <div class="mt-[0.6rem] px-[0.4rem] text-center pb-[0.6rem]">
          <img
            loading="lazy"
            src="@/assets/icons/open/arrow.svg"
            alt="down"
            class="w-[0.5rem] mx-auto"
          />
          <div
            class="text-[0.44rem] leading-[0.52rem] font-medium mt-[0.36rem]"
          >
            <div>{{ authStore.i18n("cm_nota.stepFour") }}</div>
            <div>{{ authStore.i18n("cm_nota.stepFourTitle") }}</div>
          </div>
          <div
            class="w-[fit-content] mx-auto text-[0.28rem] leading-[0.28rem] italic text-[#e50113] py-[0.2rem] px-[0.28rem] border border-[#D9D9D9] mt-[0.24rem]"
          >
            {{ authStore.i18n("cm_nota.easyPayment") }}
          </div>
          <div
            class="text-[0.36rem] leading-[0.44rem] mt-[0.24rem] text-[#666]"
          >
            {{ authStore.i18n("cm_nota.paymentMethods") }}
          </div>
          <img
            loading="lazy"
            src="@/assets/icons/open/payIcons.png"
            alt="pay"
            class="w-full mt-[0.6rem]"
          />
        </div>
      </div>
      <!-- <div
        class="w-full mt-[0.8rem] px-[0.4rem] text-[0.36rem] leading-[0.44rem] text-center page-footer"
      >
        <div class="font-medium">
          {{ authStore.i18n("cm_nota.helpSolve") }}
        </div>
        <div class="mt-[0.2rem]">
          {{ authStore.i18n("cm_nota.selectService") }}
        </div>
        <div class="text-center mt-[0.6rem]">
          <n-button
            color="#E50113"
            @click="onHandleWhatsAppClick(6)"
            data-spm-box="potential_user_note_submit"
            class="fit-content py-[0.4rem] px-[1rem] rounded-[4rem] text-[0.36rem] leading-[0.36rem] font-medium"
          >
            <div
              class="flex items-center justify-center"
              v-html="authStore.i18n('cm_nota.submitWhatsapp')"
            ></div>
          </n-button>
        </div>
      </div> -->
      <img
        loading="lazy"
        src="@/assets/icons/open/arrow.svg"
        alt="down"
        class="w-[0.48rem] mt-[0.6rem] mb-[0.44rem] mx-auto"
      />
      <div class="page-footer">
        <div
          class="w-[6rem] text-[0.44rem] leading-[0.76rem] font-semibold text-[#333] pt-[0.32rem] ml-[0.4rem] relative"
        >
          <span>Haga clic en</span>
          <span class="relative">
            <span class="text-[#25D366]"> WhatsApp</span>
            <img
              loading="lazy"
              src="@/assets/icons/open/greenLine.svg"
              alt="line"
              class="w-[3.2rem] absolute top-[0.51rem] left-[0.22rem]"
            />
          </span>
          <br />
          <span
            >para contactarnos y<br />
            obtener más información</span
          >
        </div>
        <img
          loading="lazy"
          alt="click"
          class="icon"
          @click="onHandleWhatsAppClick('bottom')"
          src="@/assets/icons/open/mobileWhatsappClick.png"
        />
        <n-button
          class="button"
          color="#E50113"
          @click="onHandleWhatsAppClick('bottom')"
          data-spm-box="potential_user_note_submit"
        >
          <div
            class="flex items-center justify-center text-[0.36rem] leading-[0.36rem] font-semibold"
            v-html="authStore.i18n('cm_nota.submitWhatsappMobile')"
          ></div>
        </n-button>
      </div>
    </div>
  </div>
  <div class="side-affix">
    <icon-card
      name="logos:whatsapp-icon"
      size="0.44rem"
      title="W/app"
      :border="true"
      :multiple="true"
      @click="onHandleWhatsAppClick()"
    ></icon-card>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";
import transImg1 from "@/assets/icons/open/transImg1.png";
import transImg2 from "@/assets/icons/open/transImg2.png";
import transImg3 from "@/assets/icons/open/transImg3.png";
import serviceImg1 from "@/assets/icons/open/serviceImg1.png";
import serviceImg2 from "@/assets/icons/open/serviceImg2.png";
import serviceImg3 from "@/assets/icons/open/serviceImg3.png";
import customerImg1 from "@/assets/icons/open/customerImg1.png";
import customerImg2 from "@/assets/icons/open/customerImg2.png";
import customerImg3 from "@/assets/icons/open/customerImg3.png";
import busImg1 from "@/assets/icons/open/busImg1.png";
import busImg2 from "@/assets/icons/open/busImg2.png";
import busImg3 from "@/assets/icons/open/busImg3.png";
import mobileVisitChina from "@/assets/icons/open/mobileVisitChina.png";
import mobileOnlineSelection from "@/assets/icons/open/mobileOnlineSelection.png";
import mobileVipCustomSelection from "@/assets/icons/open/mobileVipCustomSelection.png";
import mobileSelfShipping from "@/assets/icons/open/mobileSelfShipping.png";
import mobileDoorToDoor from "@/assets/icons/open/mobileDoorToDoor.png";
import mobilePlaceOrder from "@/assets/icons/open/mobilePlaceOrder.png";
import mobileReceiveQuality from "@/assets/icons/open/mobileReceiveQuality.png";
import mobileNotasBg from "@/assets/icons/open/mobileNotasBg.png";

const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const configStore = useConfigStore();

const companyData = [
  {
    number: authStore.i18n("cm_nota.number1"),
    numberDesc: authStore.i18n("cm_nota.spanishStaff"),
    imgData: [transImg1, transImg2, transImg3],
  },
  {
    number: authStore.i18n("cm_nota.22Years"),
    numberDesc: authStore.i18n("cm_nota.focusedOnLatAm"),
    imgData: [serviceImg1, serviceImg2, serviceImg3],
  },
  {
    number: authStore.i18n("cm_nota.number2"),
    numberDesc: authStore.i18n("cm_nota.supportedClients"),
    imgData: [customerImg1, customerImg2, customerImg3],
  },
  {
    number: authStore.i18n("cm_nota.number3"),
    numberDesc: authStore.i18n("cm_nota.associatedSuppliers"),
    imgData: [busImg1, busImg2, busImg3],
  },
];

const serviceData = [
  {
    title: authStore.i18n("cm_nota.visitChina"),
    imgUrl: mobileVisitChina,
    descData: [
      authStore.i18n("cm_nota.visitChinaDesc"),
      authStore.i18n("cm_nota.visitChinaDesc2"),
    ],
  },
  {
    title: authStore.i18n("cm_nota.onlineSelection"),
    imgUrl: mobileOnlineSelection,
    descData: [
      authStore.i18n("cm_nota.onlineSelectionDesc"),
      authStore.i18n("cm_nota.onlineSelectionDesc2"),
    ],
  },
  {
    title: authStore.i18n("cm_nota.vipCustomSelection"),
    imgUrl: mobileVipCustomSelection,
    descData: [
      authStore.i18n("cm_nota.vipCustomSelectionDesc"),
      authStore.i18n("cm_nota.vipCustomSelectionDesc2"),
    ],
  },
];

const userTypeList = [
  {
    value: "POTENTIAL_USER_TYPE_WHOLESALE",
    label: authStore.i18n("cm_nota.wholesale"),
  },
  {
    value: "POTENTIAL_USER_TYPE_RETAIL",
    label: authStore.i18n("cm_nota.retail"),
  },
  {
    value: "POTENTIAL_USER_TYPE_ONLINE_SHOP",
    label: authStore.i18n("cm_nota.personalUse"),
  },
  {
    value: "POTENTIAL_USER_TYPE_OTHER",
    label: authStore.i18n("cm_nota.other"),
  },
];

const withImportExperienceList = [
  {
    value: "true",
    label: authStore.i18n("cm_nota.yesExperience"),
  },
  {
    value: "false",
    label: authStore.i18n("cm_nota.noExperience"),
  },
];

const serviceTypeList = [
  {
    value: "POTENTIAL_SERVICE_TYPE_ONLINE_BUY",
    label: authStore.i18n("cm_nota.onlineSelection"),
    labelDesc: [
      authStore.i18n("cm_nota.onlineSelectionDesc1"),
      authStore.i18n("cm_nota.onlineSelectionDesc2"),
    ],
  },
  {
    value: "POTENTIAL_SERVICE_TYPE_VISIT_CHINA",
    label: authStore.i18n("cm_nota.visitChina"),
    labelDesc: [
      authStore.i18n("cm_nota.visitChinaDesc1"),
      authStore.i18n("cm_nota.visitChinaDesc2"),
    ],
  },
  {
    value: "POTENTIAL_SERVICE_TYPE_CUSTOMIZE_PRODUCT",
    label: authStore.i18n("cm_nota.vipCustomSelection"),
    labelDesc: [
      authStore.i18n("cm_nota.vipCustomSelectionDesc1"),
      authStore.i18n("cm_nota.vipCustomSelectionDesc2"),
    ],
  },
];

const userForm = reactive<any>({
  userType: null, // 用户类型
  userTypeRemark: null, // 用户类型备注（用户类型为“其它”，用户输入类型说明）
  withImportExperience: null, // 是否有进口经验（true:有，false:无）
  serviceType: null, // 服务类型
  contactName: null, // 联系人
  countryId: null, // 国家
  whatsapp: null, // whatsapp
  email: null, // 邮箱
  buyerRemark: null, // 留言
});

const pageData = reactive(<any>{
  activatedIndex: 0,
  countryList: <any>[],
  countryRegexps: <any>{},
  submitLoading: false,
});

const userFormRef = ref<FormInst | null>(null);
const userRules: FormRules = {
  userType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  userTypeRemark: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
  withImportExperience: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  serviceType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  contactName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  whatsapp: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
    validator(rule: FormItemRule, value: any) {
      const lengths =
        pageData.countryRegexps.phoneCount &&
        pageData.countryRegexps.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  email: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any, callback: Function) {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      // 未填写邮箱
      if (!value) {
        return callback(new Error(authStore.i18n("cm_nota.inputPlaceholder")));
      }
      // 格式不正确
      if (!pattern.test(value)) {
        return callback(new Error(authStore.i18n("cm_common.emailTips")));
      }
      return callback();
    },
  },
  captchaCode: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
};

// onBeforeMount(() => {
//   if (config.public.defaultCountryCode === "US") {
//     return navigateTo({
//       path: "/h5",
//       query: {
//         spm: window.MyStat?.getSpmCode("potential-user-shunt-home"),
//       },
//     });
//   }
// });

await onGetCountry();
async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    if (config.public.defaultCountryCode) {
      res?.data.map((country: any) => {
        if (country.countryCodeTwo === config.public.defaultCountryCode) {
          userForm.countryId = country.id;
          pageData.countryRegexps = country;
          if (pageData.countryRegexps.phoneCount) {
            userRules["whatsapp"].message = `${authStore.i18n(
              "cm_submit.whatsappTips"
            )} ${pageData.countryRegexps.phoneCount} ${authStore.i18n(
              "cm_submit.whatsapp"
            )}`;
          }
        }
      });
    }
  }
}

onGetCaptchaImage();
async function onGetCaptchaImage() {
  const res: any = await useGetCaptchaImage({ captchaPageSource: "notas" });
  if (res?.result?.code === 200) {
    pageData.captchaImage = res?.data.imageData;
  }
}

function onRefreshCaptcha() {
  onGetCaptchaImage();
  userForm.captchaCode = "";
}

// 价格高亮  匹配 “US$: xxxx” 红色标红
function newHighlightUSPrice(desc: string) {
  const regex = /(US\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g;
  return desc.replace(
    regex,
    '<span style="color: #e50113;text-wrap: nowrap;font-weight:600">$1</span>'
  );
}

// 价格高亮  匹配 “US$: xxxx” 红色标红
function highlightUSPrice(desc: string) {
  const regex = /(US\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g;
  return desc.replace(
    regex,
    '<span style="color: #e50113;text-wrap: nowrap;">$1</span>'
  );
}

function onSelectCountry(value: any, country: any, label: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${label} 选择：${country?.countryName} （原值：${
      pageData.countryRegexps.countryName || "无"
    }）`
  );

  pageData.countryRegexps = country;
  // 如果有长度校验 则校验长度
  if (pageData.countryRegexps.phoneCount) {
    userRules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappTips"
    )} ${pageData.countryRegexps.phoneCount} ${authStore.i18n(
      "cm_submit.whatsapp"
    )}`;
  } else {
    // 没有长度校验 校验必填
    userRules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappRequired"
    )}`;
  }
}

async function onSubmit(event: any) {
  try {
    // 校验表单
    const isValid = await userFormRef.value?.validate();
    if (isValid) {
      pageData.submitLoading = true;
      try {
        loadReCAPTCHA("submit", async function (success, token) {
          let paramsObj = {
            ...userForm,
            userSource: !!route?.query?.utm_source
              ? "筛选器一重筛选_" + route?.query?.utm_source
              : "筛选器一重筛选",
            reCaptchaLoadSuccess: success,
            reCaptchaToken: token,
            captchaPageSource: "notas",
          };
          const res: any = await useSaveUserInfo(paramsObj);
          pageData.submitLoading = false;
          const areaCode = pageData.countryRegexps.areaCode;
          if (res?.result?.code === 200) {
            window?.MyStat?.addPageEvent(
              "potential_user_submit_success",
              `保存潜客信息成功，顺序号：${res?.data?.seqNo}`
            );
            // if (userForm.serviceType === "POTENTIAL_SERVICE_TYPE_ONLINE_BUY") {
            navigateTo(res?.data?.whatsAppUrl, {
              external: true,
            });
            // } else {
            //   navigateTo({
            //     path: "/h5/notas/success",
            //     query: {
            //       serviceType: userForm.serviceType,
            //       contactName: userForm.contactName,
            //       whatsapp: areaCode + userForm.whatsapp,
            //       spm: window.MyStat.getPageSPM(event),
            //     },
            //   });
            // }
          } else {
            if (res?.result?.code === 952401) {
              onRefreshCaptcha();
              showToast(authStore.i18n("cm_nota.captchaCodeError"));
            } else {
              showToast(res?.result?.message);
            }
            window?.MyStat?.addPageEvent(
              "potential_user_submit_error",
              `潜客信息表单错误：${res?.result?.message}`
            );
          }
        });
      } catch (error) {
        pageData.submitLoading = false;
      }
    }
  } catch (error) {
    const remark = `${error?.[0]?.[0]?.message} [${error?.[0]?.[0]?.field}]`;
    window?.MyStat?.addPageEvent(
      "potential_user_submit_error",
      `潜客信息表单错误：${remark}`
    );
  }
}

// 下拉选择埋点事件
function onSelectEvent(value: string, attr: any, label: any) {
  userForm[attr] = value;
  let list = <any>[];
  if (attr === "userType") {
    list = userTypeList;
  }
  if (attr === "withImportExperience") {
    list = withImportExperienceList;
  }
  if (attr === "serviceType") {
    list = serviceTypeList;
  }
  const match = list.find((item: any) => item.value === value);
  if (!value) return;
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${label} 选择：${match?.label}`
  );
}

// 输入框埋点事件
async function onBlurEvent(value: string, label: any, form: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_input",
    `${label} 输入：${value}`
  );
  if (form === "captchaCode") {
    const res: any = await useCheckCaptchaImage({
      captchaPageSource: "notas",
      captchaCode: value,
    });
    if (res?.result?.code === 952401) {
      onRefreshCaptcha();
      showToast(authStore.i18n("cm_nota.captchaCodeError"));
    }
  }
}

function handleMouseOver(index: number) {
  pageData.activatedIndex = index;
}

function onScrollToNav(position: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_click_form_anchor",
    `点击潜客信息表单锚点${position}`
  );
  const element = document.getElementById("pageForm");
  if (!element) return;
  const elementRect = element.getBoundingClientRect();
  const viewportHeight =
    window.innerHeight || document.documentElement.clientHeight;
  const offsetPosition = elementRect.top + window.scrollY - viewportHeight / 2;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth",
  });
}

async function onHandleWhatsAppClick(val?: any) {
  let clickSource = "open/notas页面悬浮按钮点击WhatsApp";
  if (val) {
    if (val === "bottom") {
      clickSource = `open/notas页面正文区底部按钮点击WhatsApp`;
    } else {
      clickSource = `open/notas页面正文区第${val}个按钮点击WhatsApp`;
    }
  }
  window?.MyStat?.addPageEvent(
    "potential_user_click_whatsapp",
    clickSource,
    true
  );
  loadReCAPTCHA("submit", async function (success: any, token: any) {
    let paramsObj = {
      userSource: !!route?.query?.utm_source
        ? "筛选器一重筛选_" + route?.query?.utm_source
        : "筛选器一重筛选",
      reCaptchaLoadSuccess: success,
      reCaptchaToken: token,
      clickSource,
    };
    const res: any = await useClickWhatsapp(paramsObj);
    if (res?.result?.code === 200) {
      onWhatsAppClick();
    } else {
      showToast(res?.result?.message);
    }
  });
}
</script>

<style scoped lang="scss">
.mobile-container {
  height: auto;
  width: 100%;
  color: #333;
}

.page-header {
  position: relative;
  width: 100%;
  height: 12.2rem;
  background-image: url("@/assets/icons/open/mobileNotasBg.png");
  background-size: 100%100%;
  padding: 0.44rem 0.4rem;
  color: #fff;
}

.number-item {
  padding-left: 0.24rem;
  position: relative;
  height: fit-content;
  cursor: pointer;
  &::before {
    background-color: #333;
    border-radius: 0.04rem;
    content: "";
    display: inline-block;
    height: 100%;
    left: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 0.02rem;
  }
}
.activated-number-item {
  color: #e50113;
  &::before {
    background-color: #e50113;
  }
}

.page-form {
  width: 100%;
  border-radius: 0.4rem 0.4rem 0 0;
  background-size: 100% 5.48rem, 100% 100%;
  background-repeat: no-repeat;
  padding: 0.6rem 0.16rem;
  ul {
    margin-top: 0.2rem;
    list-style-type: disc;
    padding-left: 0.4rem;
    color: #7f7f7f;
    font-size: 0.26rem;
    line-height: 0.36rem;
    background-color: #f2f2f2;
    padding: 0.2rem 0.32rem 0.12rem 0.52rem;
    border-radius: 0.4rem;
    li {
      margin-bottom: 0.08rem;
      font-size: 0.24rem;
      line-height: 0.36rem;
    }
  }

  :deep(.n-form-item-label__text) {
    font-weight: 500;
  }
  .top-form-item {
    padding-top: 0.4rem;
    padding-bottom: 0.16rem;
    margin-bottom: 0.1rem;
    position: relative;
    border-bottom: 0.02rem solid #e6e6e6;
    :deep(.n-form-item-feedback-wrapper) {
      position: absolute;
      bottom: -0.6rem;
      left: 0;
      color: #e50113;
    }
  }

  .others-form-item {
    padding-top: 0.4rem;
    padding-bottom: 0.16rem;
    margin-bottom: 0.1rem;
    position: relative;
    border-bottom: 0.02rem solid #e6e6e6;
    :deep(.n-form-item-feedback-wrapper) {
      position: absolute;
      top: 0.12rem;
      left: 0;
      color: #e50113;
      .n-form-item-feedback__line {
        line-height: 1;
      }
    }
  }

  .inner-form-item {
    width: 5.4rem;
    margin-left: 0.16rem;
    position: absolute;
    bottom: 0rem;
    left: 1.2rem;
    :deep(.n-form-item-feedback-wrapper) {
      position: absolute;
      bottom: -0.6rem;
      left: -1.36rem;
      color: #e50113;
    }
  }

  .custom-input {
    border: none !important;
    --n-border: none !important;
    --n-border-warning: none;
    --n-border-focus-warning: none;
    --n-border-hover-warning: none;
    --n-border: none;
    --n-border-disabled: none;
    --n-border-hover: none;
    --n-border-focus: none;
    --n-box-shadow-focus: none;
    --n-box-shadow-focus-warning: none;
    --n-border-error: none;
    --n-border-focus-error: none;
    --n-border-hover-error: none;
    --n-box-shadow-focus-error: none;
    --n-border-active-error: none;
    --n-box-shadow-active: none;
    --n-border-active: none;
    :deep(.n-base-selection) {
      border: none !important;
      --n-border: none !important;
      --n-border-warning: none;
      --n-border-focus-warning: none;
      --n-border-hover-warning: none;
      --n-border: none;
      --n-border-disabled: none;
      --n-border-hover: none;
      --n-border-focus: none;
      --n-box-shadow-focus: none;
      --n-box-shadow-focus-warning: none;
      --n-border-error: none;
      --n-border-focus-error: none;
      --n-border-hover-error: none;
      --n-box-shadow-focus-error: none;
      --n-border-active-error: none;
      --n-box-shadow-active: none;
      --n-border-active: none;
    }
    :deep(.n-input-wrapper) {
      padding-left: 0;
      padding-right: 0;
    }
    :deep(.n-base-selection-input) {
      padding-left: 0;
      padding-right: 0;
    }
    :deep(.n-base-selection .n-base-selection-overlay) {
      padding-left: 0;
      padding-right: 0;
    }
    :deep(.n-input-wrapper) {
      height: 0.44rem;
      line-height: 0.44rem;
    }
    :deep(.n-form-item-blank) {
      height: 0.44rem;
      line-height: 0.44rem;
    }
    :deep(.n-input__input-el) {
      height: 0.44rem;
      line-height: 0.44rem;
    }
  }
  .input-form-item {
    :deep(.n-form-item-blank) {
      height: 0.44rem;
      line-height: 0.44rem;
      min-height: 0.44rem;
    }
  }
}

.side-affix {
  position: fixed;
  right: 0.12rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}
.page-footer {
  position: relative;
  width: 100%;
  height: 8.68rem;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/open/mobileWhatsappBg.png");
  .icon {
    position: absolute;
    top: 2.8rem;
    width: 2.8rem;
    right: 0.32rem;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 4.64rem;
    right: 0.4rem;
    height: fit-content;
    display: inline-flex;
    padding: 0.32rem 0.68rem;
    align-items: center;
    gap: 0.08rem;
    border-radius: 12.96rem;
    border: 0.06rem solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0 0.02rem 0.04rem 0 rgba(0, 0, 0, 0.5);
  }
}
</style>
