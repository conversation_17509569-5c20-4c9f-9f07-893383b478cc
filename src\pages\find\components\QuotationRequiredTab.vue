<template>
  <div class="py-[30px] px-[20px] bg-white rounded-[12px]">
    <div class="flex gap-[10px]">
      <!-- 左侧商品列表 -->
      <div class="flex-1">
        <div
          class="text-[28px] leading-[28px] font-medium"
          :class="props.showTabs ? 'text-[#1B80E4]' : 'text-[#333]'"
        >
          {{ authStore.i18n("cm_find.inquiryList") }}
        </div>
        <div v-if="cartData?.goodsList?.length" class="mt-[30px] pr-[10px]">
          <!-- 全选 -->
          <n-checkbox
            size="large"
            v-model:checked="selectAll"
            class="text-[16px] leading-[16px] font-medium mb-[28px] w-full"
            @update:checked="onAllSelection"
          >
            <n-divider class="divider" title-placement="left">
              {{ authStore.i18n("cm_find.selectAllItems") }}
              (<span>{{ cartData.stat?.skuCount || 0 }}</span
              >)
            </n-divider>
          </n-checkbox>

          <!-- 商品列表 -->
          <div
            v-for="(goods, index) in cartData.goodsList"
            :key="goods.goodsId"
            class="mb-4"
          >
            <div class="flex">
              <n-checkbox
                class="mr-2"
                v-model:checked="goods.selected"
                @update:checked="
                  (value) => $emit('onGoodsSelection', value, goods)
                "
              />
              <goods-card
                :goods="goods"
                class="flex-1 mr-10"
                :spmIndex="index"
                spmCode="cart-goods-list"
              />
              <icon-card
                name="uil:trash-alt"
                color="#797979"
                size="28"
                @click="$emit('onDeleteGoods', goods)"
              />
            </div>

            <!-- SKU列表 -->
            <div
              v-for="sku in goods.skuList"
              :key="sku.skuId"
              class="sku-checkbox mb-3 ml-10"
            >
              <div class="flex items-center">
                <n-checkbox
                  v-model:checked="sku.selected"
                  @update:checked="
                    (value) => $emit('onSkuSelection', value, sku, goods)
                  "
                  class="flex-1"
                />
                <sku-card
                  :sku="sku"
                  :goods="goods"
                  @onCartQtyUpdate="
                    (value) => $emit('onCartQtyUpdate', value, sku, goods)
                  "
                  :step="sku.minIncreaseQuantity"
                >
                  <icon-card
                    name="iconamoon:arrow-right-2"
                    color="#797979"
                    size="26"
                    class="ml-4 cursor-pointer"
                    @click.stop="$emit('onOpenSkuDialog', sku, goods)"
                  />
                </sku-card>
                <icon-card
                  name="uil:trash-alt"
                  color="#797979"
                  size="28"
                  @click="$emit('onDeleteSku', sku, goods)"
                />
              </div>
            </div>
            <n-divider v-if="index !== cartData.goodsList.length - 1" />
          </div>
        </div>

        <!-- 空状态 -->
        <n-empty
          v-else
          :description="authStore.i18n('cm_find.noData') || 'No hay datos'"
          class="mt-24"
        >
          <template #extra>
            <n-button
              size="small"
              color="#E50113"
              text-color="#fff"
              @click="onGoHome"
            >
              {{ authStore.i18n("cm_find.goHome") || "Ir a Inicio" }}
            </n-button>
          </template>
        </n-empty>
      </div>

      <!-- 右侧费用明细 -->
      <div class="w-[400px]">
        <div class="bg-white rounded-lg sticky top-5 right-0">
          <n-affix :trigger-top="20" class="h-fit" id="submit-affix">
            <div
              class="w-[400px] flex flex-col gap-[24px] px-[20px] py-[24px] text-[16px] leading-[16px] text-[#1A1A1A]"
            >
              <div
                class="text-[18px] leading-[18px] font-medium"
                :class="props.showTabs ? 'text-[#1B80E4]' : 'text-[#1A1A1A]'"
              >
                {{ authStore.i18n("cm_find.preQuoteSummary") }}
              </div>

              <country-select
                mode="popover"
                @save="onSaveCountry"
                spm="select_site_from_cart"
                class="find-country-select"
              />

              <div class="flex justify-between text-[16px] leading-[16px]">
                <span>{{ authStore.i18n("cm_find_quantityOfUnits") }}</span>
                <span>
                  <span class="font-medium">{{
                    cartData.stat?.selectSkuTotalQuantity
                  }}</span>
                  {{
                    cartData.stat?.selectSkuTotalQuantity > 1
                      ? authStore.i18n("cm_find_totalSkuUnits")
                      : authStore.i18n("cm_find_totalSkuUnit")
                  }}
                </span>
              </div>
              <div class="flex justify-between text-[16px] leading-[16px]">
                <span>{{ authStore.i18n("cm_find_itemsCost") }}:</span
                ><span
                  class="text-[#e50113] font-medium"
                  v-if="
                    cartData.stat?.selectTotalSalePrice ||
                    cartData.stat?.selectTotalSalePrice === 0
                  "
                  >{{ setUnit(cartData.stat?.selectTotalSalePrice) }}</span
                >
              </div>

              <div
                class="flex justify-between text-[16px] leading-[16px] text-[#4D4D4D]"
              >
                <n-popover trigger="hover" raw>
                  <template #trigger>
                    <div class="flex items-center cursor-pointer">
                      <img
                        class="w-[14px] h-[14px] mr-[2px]"
                        src="@/assets/icons/common/alert-circle.svg"
                        :alt="authStore.i18n('cm_goods.estimatedShippingCost')"
                        referrerpolicy="no-referrer"
                      />
                      {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
                    </div>
                  </template>
                  <div
                    style="
                      z-index: 1;
                      width: 300px;
                      padding: 6px 14px;
                      background-color: #fff4d4;
                      transform-origin: inherit;
                      border: 1px solid #f7ba2a;
                    "
                  >
                    {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
                  </div>
                </n-popover>

                <span v-if="cartData?.totalEstimateFreight">{{
                  setUnit(cartData.totalEstimateFreight)
                }}</span>
                <span v-else>{{
                  authStore.i18n("cm_goods.pendingConfirmation")
                }}</span>
              </div>
            </div>
            <div class="w-[400px] mt-[16px]">
              <div
                class="text-[14px] leading-[14px] text-[#A6A6A6] text-center"
              >
                {{ authStore.i18n("cm_find_confirmWithoutPay") }}
              </div>

              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                @click="onGoFindSubmit($event)"
                data-spm-box="cart-to-checkout"
                class="rounded-[6px] w-full h-[44px] my-[12px]"
              >
                <img
                  src="@/assets/icons/common/list.svg"
                  alt="list"
                  class="mr-[8px]"
                  referrerpolicy="no-referrer"
                />
                <span class="text-[16px] leading-[16px]">{{
                  authStore.i18n("cm_find.inquireNow")
                }}</span>
              </n-button>
              <div
                class="text-[14px] leading-[16px] text-[#A6A6A6] text-center"
              >
                {{ authStore.i18n("cm_find_submitTip") }}
              </div>
            </div>
          </n-affix>
        </div>
      </div>
    </div>
  </div>
  <!-- 2000美元提交校验提示 -->
  <n-modal :show="pageData.submitDialogVisible" :show-icon="false">
    <n-card
      :bordered="false"
      style="
        width: 580px;
        color: #000;
        padding: 16px 10px !important;
        text-align: center;
      "
    >
      <div class="text-[18px] mb-[24px] px-[22px]">
        El importe de su producto es inferior a US$ 2000,
        <span class="text-[#e50113]"
          >los gastos de envío pueden ser más caros que el coste del
          producto</span
        >, se recomienda aumentar la compra a US$ 2000.
      </div>
      <div class="flex justify-between">
        <n-button
          round
          color="#fff"
          text-color="#000"
          data-spm-box="cart-to-checkout"
          @click="onConfirmSubmit($event, 'dialog')"
          class="border-btn w-[240px] h-[36px] text-[16px]"
        >
          {{ authStore.i18n("cm_common.buySubmit") }}</n-button
        >
        <n-button
          round
          color="#E50113"
          text-color="#fff"
          class="w-[240px] h-[36px] text-[16px]"
          @click="onCancelSubmit"
        >
          {{ authStore.i18n("cm_common.buyAgain") }}</n-button
        >
      </div>
    </n-card>
  </n-modal>

  <!-- 错误弹窗 -->
  <ErrorModal
    :message="pageData.errorMessage"
    v-model:visible="pageData.errorDialogVisible"
  />
  <SubmitLoadingModal :show="pageData.submitLoading" />
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import GoodsCard from "./GoodsCard.vue";
import SkuCard from "./SkuCard.vue";
import ErrorModal from "./ErrorModal.vue";
import SubmitLoadingModal from "./SubmitLoadingModal.vue";

const authStore = useAuthStore();

const props = defineProps({
  cartData: {
    type: Object,
    default: () => {},
  },
  showTabs: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "onAllSelection",
  "onGoodsSelection",
  "onSkuSelection",
  "onCartQtyUpdate",
  "onDeleteGoods",
  "onDeleteSku",
  "onOpenSkuDialog",
  "onGoLoginRegister",
]);

const pageData = reactive<any>({
  errorMessage: "",
  errorDialogVisible: false,
  submitDialogVisible: false,
  submitLoading: false,
});

// 计算全选状态
const selectAll = computed(() => {
  return (
    props.cartData?.goodsList?.every((goods: any) => goods.selected) || false
  );
});

// 全选操作
function onAllSelection(value: boolean) {
  emit("onAllSelection", value);
}

function onGoFindSubmit(event: any) {
  (window as any)?.MyStat?.addPageEvent(
    "click_start_looking",
    `点击创建询盘按钮`
  ); // 埋点
  const currentStat = props.cartData?.stat;
  if (currentStat?.selectTotalSalePrice < 2000) {
    pageData.submitDialogVisible = true;
    (window as any)?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_open",
      `未满2000美元对话框-打开`
    ); // 埋点
    return;
  }
  onConfirmSubmit(event);
}

async function onConfirmSubmit(event: any, from?: any) {
  pageData.submitLoading = true;
  if (from) {
    (window as any)?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_ignore",
      `未满2000美元对话框-忽略`
    ); // 埋点
  }
  (window as any)?.MyStat?.addPageEvent(
    "carrito_click_inquiry",
    `购物车列表进入询盘提交页`
  ); // 埋点

  const selectedSkuList = <any>[];
  const currentGoodsList = props.cartData?.goodsList || [];
  currentGoodsList.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });

  if (!!(window as any)?.fbq) {
    (window as any)?.fbq("track", "InitiateCheckout", {
      currency: "USD",
      num_items: selectedSkuList?.length,
      contents: selectedSkuList,
    });
  }
  if (!!(window as any)?.ttq) {
    (window as any)?.ttq?.track("InitiateCheckout", {
      currency: "USD",
      value: props.cartData?.stat?.selectTotalSalePrice,
      content_type: "product",
      description: JSON.stringify(selectedSkuList),
    });
  }

  const res: any = await useGetInquiry({
    params: selectedSkuList,
    siteId: window.siteData.siteInfo.id,
  });
  if (res?.result?.code === 200) {
    const inquiryInfo = res?.data;
    await authStore.setInquiryInfo(inquiryInfo);
    navigateToPage(`/find/submit`, {}, false, event);
  } else if (res?.result?.code === 403) {
    emit("onGoLoginRegister");
  } else {
    window?.MyStat?.addPageEvent(
      "submit_start_looking_error",
      `进入询盘信息页错误：${res?.result?.message}`
    ); // 埋点
    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

function onCancelSubmit() {
  window?.MyStat?.addPageEvent(
    "less_then_2000usd_dialog_close",
    `未满2000美元对话框-关闭`
  ); // 埋点
  pageData.submitDialogVisible = false;
}

function onGoHome() {
  window.location.href = "/";
}

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};
</script>

<style scoped lang="scss">
.sku-checkbox {
  background: #f2f2f2;
  padding: 6px 4px 6px 10px;
  .sku-container {
    padding: 0 !important;
  }
}

:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}

:deep(.n-checkbox__label) {
  width: 100%;
  padding: 0 !important;
}

:deep(.divider .n-divider) {
  margin: 0px;
}

:deep(.n-divider__title) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}

.divider.n-divider:not(.n-divider--vertical) {
  margin: 0px;
}

:deep(.divider .n-divider__line.n-divider__line--left) {
  display: none;
}

.border-btn {
  border: 1px solid #c7c7c7;
}
.border-btn:hover {
  background: #e50113;
  color: #fff;
  border: none;
}
</style>
