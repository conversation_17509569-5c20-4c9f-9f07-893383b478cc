<template>
  <div class="bg-[#F2F2F2] min-h-[100vh] pt-[1rem] text-[0.28rem]">
    <div
      class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] items-center justify-center bg-white fixed top-0 bg-white z-10"
    >
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        data-spm-box="navigation-back-icon"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_user.direction") }}
      </div>
    </div>
    <div class="p-[0.2rem] pb-[1.8rem] h-full overflow-auto">
      <n-space
        vertical
        :style="{ gap: '0.2rem 0' }"
        v-if="pageData.addressList?.length"
      >
        <div
          class="bg-white py-[0.2rem] rounded-[0.08rem]"
          v-for="address in pageData.addressList"
          :key="address.id"
        >
          <div class="flex justify-between items-center px-[0.2rem]">
            <div class="mr-[0.24rem]">
              <div class="flex mb-[0.12rem]">
                <n-ellipsis :line-clamp="1" :tooltip="false" class="break-all">
                  {{ address.contactName }}
                </n-ellipsis>
                <div class="ml-[0.2rem] flex-shrink-0">{{ address.phone }}</div>
              </div>
              <n-ellipsis
                :line-clamp="2"
                :tooltip="false"
                class="text-[0.26rem] break-all"
              >
                {{ address.fullAddress }}
              </n-ellipsis>
            </div>
            <icon-card
              color="#555"
              size="0.48rem"
              name="iconoir:nav-arrow-right"
              data-spm-box="navigation-back-icon"
              @click="onOpenAddAddr(address)"
            ></icon-card>
          </div>
          <div
            class="border-t border-gray-200 mt-[0.3rem] pt-[0.3rem] px-[0.2rem] flex justify-between"
          >
            <div
              v-if="address.isDefault"
              class="text-[#f0a020] bg-[#f0a02026] px-[0.14rem] h-[0.56rem] leading-[0.56rem]"
            >
              {{ authStore.i18n("cm_addr.defaultAddr") }}
            </div>
            <n-checkbox
              v-else
              class="text-[0.26rem]"
              v-model:checked="address.isDefault"
              @update:checked="onAddrToDefault(address)"
              >{{ authStore.i18n("cm_addr.defaultAddr") }}</n-checkbox
            >
            <div class="flex" @click="onOpenAddAddr(address)">
              <div class="flex">
                <icon-card
                  color="#555"
                  size="0.4rem"
                  name="la:edit-solid"
                ></icon-card>
                <div class="ml-[0.05rem] leading-[0.5rem]">
                  {{ authStore.i18n("cm_addr.editAddr") }}
                </div>
              </div>
              <div class="flex ml-[0.28rem]" @click.stop="onOpenDelDg(address)">
                <icon-card
                  color="#555"
                  size="0.3rem"
                  name="uiw:delete"
                ></icon-card>
                <div class="ml-[0.05rem] leading-[0.5rem]">
                  {{ authStore.i18n("cm_addr.delAddr") }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-space>
      <div v-else>
        <n-empty
          :description="authStore.i18n('cm_address.noData')"
          class="mt-[1.28rem]"
        >
        </n-empty>
      </div>
    </div>

    <div class="fixed bottom-0 bg-white w-full px-[0.2rem] py-[0.3rem]">
      <n-button
        color="#E50113"
        text-color="#fff"
        class="rounded-[0.4rem] w-full p-[0.4rem] text-[0.28rem] mx-auto"
        @click="onOpenAddAddr()"
      >
        <div class="w-full whitespace-normal">
          {{ authStore.i18n("cm_addr.addAddress") }}
        </div>
      </n-button>
    </div>
  </div>
  <n-drawer
    v-model:show="pageData.dialogVisible"
    resizable
    default-width="100%"
    default-height="90%"
    placement="bottom"
    :on-after-leave="onCancel"
  >
    <n-drawer-content closable>
      <template #header>
        <div
          class="text-[0.3rem] leading-[0.48rem] font-medium mb-[0.3rem] pt-[0.3rem]"
        >
          {{ authStore.i18n("cm_addr.addOrEdit") }}
        </div>
      </template>
      <div class="mt-[0.3rem]">
        <n-form
          :model="editForm"
          ref="editFormRef"
          :rules="rules"
          label-placement="top"
          label-width="1.8rem"
        >
          <!-- 联系人 -->
          <n-form-item
            path="contactName"
            :label="authStore.i18n('cm_addr.contact')"
          >
            <n-input
              v-trim
              clearable
              v-model:value="editForm.contactName"
              :placeholder="authStore.i18n('cm_addr.contactPh')"
            ></n-input>
          </n-form-item>
          <!-- 联系方式 -->
          <n-form-item path="phone" :label="authStore.i18n('cm_addr.phone')">
            <n-input
              v-trim
              clearable
              :input-props="{ type: 'number' }"
              v-model:value="editForm.phone"
              :placeholder="authStore.i18n('cm_addr.phonePh')"
            ></n-input>
          </n-form-item>
          <!-- 国家 -->
          <n-form-item
            path="countryId"
            :label="authStore.i18n('cm_addr.country')"
          >
            <n-select
              clearable
              filterable
              value-field="id"
              label-field="countryEsName"
              :options="pageData.countryList"
              v-model:value="editForm.countryId"
              @update:value="onSelectCountry('update')"
              :placeholder="authStore.i18n('cm_addr.countryPh')"
            />
          </n-form-item>
          <!-- 邮政编码 -->
          <n-form-item
            :label="authStore.i18n('cm_addr.postcode')"
            path="postcode"
          >
            <n-input
              v-trim
              clearable
              v-model:value="editForm.postcode"
              :input-props="{ type: 'number' }"
              :placeholder="authStore.i18n('cm_addr.postcodePh')"
            ></n-input>
          </n-form-item>
          <!-- 州/城市 -->
          <n-form-item
            path="provinceCity"
            :label="authStore.i18n('cm_addr.province')"
          >
            <!-- 省 -->
            <n-select
              tag
              filterable
              style="width: 50%"
              value-field="name"
              label-field="name"
              children-field="child"
              :options="pageData.provinceList"
              v-model:value="editForm.province"
              @update:value="onSelectProvince('update')"
              :placeholder="authStore.i18n('cm_addr.provincePh')"
            >
            </n-select>
            <!-- 市 -->
            <n-select
              tag
              filterable
              value-field="name"
              label-field="name"
              :options="pageData.cityList"
              v-model:value="editForm.city"
              style="width: 50%; margin-left: 0.2rem"
              :placeholder="authStore.i18n('cm_addr.cityPh')"
            >
            </n-select>
          </n-form-item>
          <!-- 区 -->
          <n-form-item
            :label="authStore.i18n('cm_addr.regionCode')"
            path="region"
          >
            <n-input
              v-trim
              clearable
              v-model:value="editForm.region"
              :placeholder="authStore.i18n('cm_addr.regionCodePh')"
            ></n-input>
          </n-form-item>
          <!-- 街道 -->
          <n-form-item :label="authStore.i18n('cm_addr.street')" path="street">
            <n-input
              v-trim
              clearable
              v-model:value="editForm.street"
              :placeholder="authStore.i18n('cm_addr.streetPh')"
            ></n-input>
          </n-form-item>
          <!-- 详细地址 -->
          <n-form-item
            :label="authStore.i18n('cm_addr.address')"
            path="address"
          >
            <n-input
              v-trim
              clearable
              v-model:value="editForm.address"
              :placeholder="authStore.i18n('cm_addr.addressPh')"
            ></n-input>
          </n-form-item>
          <!-- 门牌号 -->
          <n-form-item :label="authStore.i18n('cm_addr.houseNo')">
            <n-input
              v-trim
              clearable
              v-model:value="editForm.houseNo"
              :placeholder="authStore.i18n('cm_addr.houseNoPh')"
            ></n-input>
          </n-form-item>
          <!-- 参考地标 -->
          <n-form-item :label="authStore.i18n('cm_addr.referLandmark')">
            <n-input
              v-trim
              clearable
              v-model:value="editForm.referLandmark"
              :placeholder="authStore.i18n('cm_addr.referLandmarkPh')"
            ></n-input>
          </n-form-item>
          <!-- 地址标签 -->
          <n-form-item :label="authStore.i18n('cm_addr.addressLabel')">
            <n-radio-group v-model:value="editForm.addressLabel">
              <n-radio-button value="ADDRESS_LABEL_HOME">{{
                authStore.i18n("cm_addr.home")
              }}</n-radio-button>
              <n-radio-button value="ADDRESS_LABEL_COMPANY">{{
                authStore.i18n("cm_addr.business")
              }}</n-radio-button>
            </n-radio-group>
          </n-form-item>
          <!-- <n-form-item label=" "> -->
          <n-checkbox
            class="ml-[0.01rem] mb-[0.4rem]"
            :label="authStore.i18n('cm_addr.defaultFlag')"
            v-model:checked="editForm.isDefault"
          ></n-checkbox>
          <!-- </n-form-item> -->
        </n-form>
      </div>
      <template #footer>
        <div class="w-full">
          <n-button
            color="#E50113"
            text-color="#fff"
            class="rounded-[0.4rem] w-full p-[0.4rem] text-[0.28rem] mx-auto"
            @click="onAddAddr"
          >
            <template #icon> </template>
            <div class="w-full whitespace-normal">
              {{ authStore.i18n("cm_addr.setAddr") }}
            </div>
          </n-button>
        </div>
      </template>
    </n-drawer-content></n-drawer
  >
  <n-modal
    preset="dialog"
    :closable="false"
    :show-icon="false"
    v-model:show="pageData.delDialogVisible"
    :on-close="onCloseDelDg"
    :on-esc="onCloseDelDg"
    :on-mask-click="onCloseDelDg"
    ><div class="text-center">{{ authStore.i18n("cm_addr.delAddrTip") }}</div>
    <div class="flex mt-[0.36rem]">
      <n-button
        ghost
        color="#fff"
        text-color="#000"
        class="rounded-[0.4rem] py-[0.3rem] px-[0.64rem] text-[0.28rem] mx-auto border border-solid border-[#b7b7b7]"
        @click="onCloseDelDg"
      >
        <div class="w-full whitespace-normal">
          {{ authStore.i18n("cm_addr.cancelBtn") }}
        </div>
      </n-button>
      <n-button
        type="primary"
        class="rounded-[0.4rem] py-[0.3rem] px-[0.64rem] text-[0.28rem] mx-auto"
        @click="onDelAddr"
      >
        <div class="w-full whitespace-normal">
          {{ authStore.i18n("cm_addr.confirmBtn") }}
        </div>
      </n-button>
    </div></n-modal
  >
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormRules, FormItemRule } from "naive-ui";

const router = useRouter();
const authStore = useAuthStore();
const editForm = reactive(<any>{});
const editFormRef = ref<FormInst | null>(null);
const pageData = reactive(<any>{
  addressList: <any>[],
  countryList: <any>[],
  provinceList: <any>[],
  cityList: <any>[],
  currentDelAddr: <any>{},
  isPreview: false,
  dialogVisible: false,
  delDialogVisible: false,
});

const rules: FormRules = {
  contactName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.contactPh"),
  },
  phone: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.phonePh"),
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.country"),
  },
  postcode: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.postcodePh"),
  },
  provinceCity: {
    required: true,
    trigger: "change",
    validator(rule: FormItemRule, value: any) {
      if (!editForm.province) {
        return new Error(authStore.i18n("cm_addr.provincePh"));
      }
      if (!editForm.city) {
        return new Error(authStore.i18n("cm_addr.cityPh"));
      }
      return true;
    },
  },
  region: {
    required: true,
    trigger: "change",
    message: authStore.i18n("cm_addr.regionCodePh"),
  },
  street: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.streetPh"),
  },
  address: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.addressPh"),
  },
};

onListUserAddress();
onGetCountry();
async function onListUserAddress() {
  const res: any = await useListUserAddress({});
  if (res?.result?.code === 200) {
    pageData.addressList = res?.data;
  } else if (res?.result?.code === 403) {
    window.location.href = "/";
  }
}

async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
  }
}

async function onOpenAddAddr(val?: any) {
  Object.keys(editForm).forEach((key) => {
    delete editForm[key];
  });
  let eventName, remark;
  if (val) {
    Object.assign(editForm, val);
    // 优先处理邮政编码和省份选择
    if (editForm.countryId) {
      // if (editForm.postcode) {
      //   await onInputPostcode();
      // } else {
      await onSelectCountry();
      // }
      if (pageData.provinceList && editForm.province) {
        onSelectProvince();
      }
    }
    eventName = "address_open_edit";
    remark = "打开地址编辑窗口";
  } else {
    eventName = "address_open_add";
    remark = "打开地址添加窗口";
  }
  window?.MyStat?.addPageEvent(eventName, remark); // 埋点

  if (!editForm.addressLabel) {
    editForm.addressLabel = "ADDRESS_LABEL_HOME";
  }
  pageData.dialogVisible = true;
}

function onCloseAddAddr() {
  pageData.dialogVisible = false;
}

async function onSelectCountry(type?: any) {
  const res: any = await useListRegionByCountry({ id: editForm.countryId });
  if (res?.result?.code === 200) {
    onHandleRegion(res?.data, type);
  } else {
    onHandleRegion([], type);
    showToast(res.result?.message);
  }
}

async function onInputPostcode(type?: any) {
  const res: any = await useListRegionByPostcode({
    countryId: editForm.countryId,
    postcode: editForm.postcode,
  });
  if (res?.result?.code === 200) {
    onHandleRegion(res?.data, type);
  } else {
    onHandleRegion([], type);
    showToast(res.result?.message);
  }
}

function onHandleRegion(data: any, type?: any) {
  pageData.provinceList = data;
  pageData.cityList = [];
  if (type === "update") {
    editForm.province = "";
    editForm.city = "";
  }
}

function onSelectProvince(type?: any) {
  const matchProvince = pageData.provinceList.find(
    (item: any) => item.name === editForm.province
  );
  pageData.cityList = matchProvince?.children;
  if (type === "update") {
    editForm.city = "";
  }
}

async function onAddAddr() {
  await editFormRef.value?.validate();
  const matchProvince = pageData.provinceList.find(
    (item: any) => item.name === editForm.province
  );
  const matchCity = matchProvince?.children?.find(
    (item: any) => item.name === editForm.city
  );

  const provinceCode = matchProvince?.code;
  const cityCode = matchCity?.code;
  let params = {
    ...editForm,
    addressLabel: editForm.addressLabel === "ADDRESS_LABEL_HOME" ? 0 : 1,
    cityCode,
    provinceCode,
  };
  const res: any = await useSaveUserAddress(params);
  if (res?.result?.code === 200) {
    onCloseAddAddr();
    onListUserAddress();

    const isEdit = Boolean(editForm.id);
    const successMessage = isEdit
      ? authStore.i18n("cm_addr.editSuccess")
      : authStore.i18n("cm_addr.addSuccess");

    const eventName = isEdit ? "address_save_edit" : "address_save_add";
    const remark = isEdit ? "地址编辑保存成功" : "地址添加保存成功";

    showToast(successMessage); //成功提示
    window?.MyStat?.addPageEvent(eventName, remark); //埋点
  } else {
    showToast(res.result?.message);
  }
}

function onCloseDelDg() {
  pageData.delDialogVisible = false;
}

function onOpenDelDg(val: any) {
  pageData.currentDelAddr = val;
  pageData.delDialogVisible = true;
  window?.MyStat?.addPageEvent("address_open_delete", "打开地址删除确认框"); // 埋点
}

async function onDelAddr() {
  let params = {
    id: pageData.currentDelAddr?.id,
  };
  const res: any = await useDeleteUserAddress(params);
  if (res?.result?.code === 200) {
    onListUserAddress();
    onCloseDelDg();
    showToast(authStore.i18n("cm_addr.delSuccess"));
    window?.MyStat?.addPageEvent("address_save_delete", "地址删除保存成功"); // 埋点
  } else {
    showToast(res.result?.message);
  }
}

async function onAddrToDefault(addr: any) {
  let params = {
    id: addr.id,
  };
  const res: any = await useAddressToDefault(params);
  if (res?.result?.code === 200) {
    onListUserAddress();
    showToast(authStore.i18n("cm_addr.editSuccess"));
  } else {
    showToast(res.result?.message);
  }
}

function onCancel() {
  pageData.dialogVisible = false;
}

// 返回
function onBackClick() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
:deep(.n-checkbox .n-checkbox-box) {
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 50%;
}
:deep(.n-checkbox__label) {
  padding-left: 0.08rem;
}
:deep(.n-drawer-header) {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 0.2rem !important;
  padding-right: 0.2rem !important;
  .n-drawer-header__main {
    margin: 0 auto;
  }
}

:deep(.n-drawer-footer) {
  border: none !important;
  padding: 0.2rem 0.3rem !important;
}
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 0.3rem 0 0.2rem !important;
}
:deep(.n-form-item-label__text),
:deep(.n-checkbox__label),
:deep(.n-radio__label),
:deep(.n-input__input-el),
:deep(.n-base-selection-label) {
  font-size: 0.26rem;
}

:deep(.n-input__placeholder),
:deep(.n-form-item-feedback__line) {
  font-size: 0.24rem;
}
</style>
