<template>
  <div class="page-wrapper">
    <div class="bg-white">
      <search-card></search-card>
    </div>
    <div class="page-content">
      <!-- 订单取消 -->
      <div
        v-if="pageData.mallOrderStatus === 'MALL_CANCELED'"
        class="bg-white rounded-lg my-3 p-6 flex justify-center items-center"
      >
        <img
          loading="lazy"
          alt="orderCancel"
          src="@/assets/icons/order/orderCancel.svg"
          class="w-[50px] h-[50px] mr-3"
          referrerpolicy="no-referrer"
        />
        <span class="font-medium text-xl text-[#e50113]">{{
          pageData.statusDesc
        }}</span>
      </div>
      <!-- 订单步骤 -->
      <order-step
        v-else
        :payMode="pageData.payMode"
        :quotationMode="pageData.quotationMode"
        :mallOrderStatus="pageData.mallOrderStatus"
      ></order-step>
      <!-- 地址信息 -->
      <div class="bg-white rounded-lg my-3 p-5">
        <div class="text-lg font-medium mb-3">
          {{ authStore.i18n("cm_order.deliveryAddress") }}
        </div>
        <div class="border-1 border-[#D7D7D7] p-3 flex items-center rounded">
          <icon-card
            name="mdi:address-marker"
            size="30"
            color="#e50113"
            class="mr-2"
          >
          </icon-card>
          <div>
            <div class="mb-1.5">
              <span class="font-medium mr-8">{{
                pageData?.addressInfo?.userName
              }}</span>
              <span>{{ pageData?.addressInfo?.phone }}</span>
            </div>
            <div>
              {{ pageData?.addressInfo?.address }}
            </div>
          </div>
        </div>
      </div>
      <!-- 物流信息 开单一期不做 暂时隐藏 -->
      <div class="bg-white rounded-lg my-3 p-5 hidden">
        <div class="text-lg font-medium mb-3">物流信息</div>
        <table class="w-full text-center">
          <thead>
            <tr>
              <!-- 最新轨迹时间 -->
              <th>最新轨迹时间</th>
              <!-- 物流公司 -->
              <th>物流公司</th>
              <!-- 物流单号 -->
              <th>物流单号</th>
              <!-- 操作 -->
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <!--规格 -->
              <td>2024-08-20 16:12:30</td>
              <!-- 物流公司 -->
              <td>HYUNDAI PLATINUM 0078W</td>
              <!-- 物流单号 -->
              <td>HDMU5520711</td>
              <!--操作 -->
              <td class="text-blue cursor-pointer" @click="onOpenLogDetail">
                查看详情
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 运输报价 -->
      <div
        class="bg-white rounded-lg my-3 p-5"
        v-if="pageData.transportAmountList?.length > 0"
      >
        <div
          class="flex justify-between mb-3"
          v-if="
            pageData?.sourcePlatform === 'ORDER_SOURCE_PLATFORM_ONLINE_MALL'
          "
        >
          <div class="text-lg font-medium">
            {{ authStore.i18n("cm_order.shippingOption") }}
          </div>
        </div>
        <div class="flex justify-between mb-3" v-else>
          <div class="text-lg font-medium">
            {{ authStore.i18n("cm_order.chooseTransport") }}
          </div>
          <div class="text-[#e50113] cursor-pointer" @click="onOpenLoadTrans">
            <icon-card
              name="material-symbols:help"
              size="20"
              color="#e50113"
              class="mr-1"
            >
            </icon-card
            ><span>{{ authStore.i18n("cm_order.WETrans") }}</span>
          </div>
        </div>

        <n-radio-group
          v-model:value="pageData.transportId"
          :on-update:value="onUpdateTrans"
          :disabled="!isPayFee"
          class="w-full"
        >
          <n-collapse
            :show-arrow="false"
            display-directive="show"
            :trigger-areas="['extra']"
            class="flex flex-wrap justify-between mt-3 px-24"
            :default-expanded-names="pageData.expandedTransport"
          >
            <div
              class="transport-box"
              :class="
                pageData.transportId === transport.transportId
                  ? '!border-[#e50113]'
                  : ''
              "
              v-for="transport in pageData.transportAmountList"
              :key="transport.transportId"
            >
              <n-collapse-item :name="transport.transportId">
                <template #header>
                  <n-radio :value="transport.transportId">
                    <span class="text-[18px]">
                      {{ transport.name }}
                    </span>
                  </n-radio>
                </template>
                <template #header-extra="{ collapsed }">
                  <icon-card
                    v-if="collapsed"
                    name="uil:angle-down"
                    color="#797979"
                    size="32"
                  ></icon-card>
                  <icon-card
                    v-else
                    name="uil:angle-up"
                    color="#797979"
                    size="32"
                  ></icon-card>
                </template>
                <template #arrow><span class="hidden"></span></template>

                <n-space vertical :style="{ gap: '20px 0' }">
                  <n-space vertical :style="{ gap: '12px 0' }">
                    <div class="flex justify-between">
                      <span>{{ authStore.i18n("cm_order.feeAmount") }}</span>
                      <div>
                        <n-popover
                          trigger="hover"
                          v-if="transport?.amountDetailList?.length > 0"
                        >
                          <template #trigger>
                            <n-button
                              color="#F6D2D4"
                              text-color="#E50013"
                              class="rounded-[4px] mr-3 h-[24px]"
                            >
                              <span class="text-[12px]">{{
                                authStore.i18n("cm_order.feeAmountDetails")
                              }}</span>
                            </n-button>
                          </template>
                          <div
                            v-for="(
                              amount, index
                            ) in transport?.amountDetailList"
                            :key="index"
                            class="flex items-center justify-between my-1"
                          >
                            <span>
                              {{ amount.feeName }}
                            </span>
                            <div
                              class="font-medium min-w-[80px] text-right ml-4"
                            >
                              {{ setUnit(amount.feeAmount) }}
                            </div>
                          </div>
                        </n-popover>

                        <span class="font-medium">{{
                          setUnit(transport.amount)
                        }}</span>
                      </div>
                    </div>
                    <div class="flex justify-between">
                      <span>{{
                        authStore.i18n("cm_order.expectDeliveryTime")
                      }}</span>
                      <span class="font-medium">{{
                        transport.expectDeliveryTime
                      }}</span>
                    </div>
                  </n-space>
                  <div class="min-h-[46px]">
                    <div
                      class="text-[#868686]"
                      v-if="transport.transportRemark"
                    >
                      <div class="mb-1">
                        {{ authStore.i18n("cm_order.shippingInformation") }}
                      </div>
                      <div>{{ transport.transportRemark }}</div>
                    </div>
                  </div>
                </n-space>
              </n-collapse-item>
            </div>
          </n-collapse>
        </n-radio-group>
      </div>
      <!-- 商品信息 -->
      <div class="bg-white rounded-lg my-3 p-5">
        <div class="text-lg font-medium mb-3">
          {{ authStore.i18n("cm_order.productDetails") }}
        </div>
        <div class="my-2">
          <span class="text-[#666]">{{
            authStore.i18n("cm_order.orderNo")
          }}</span
          ><span>{{ pageData.orderNo }}</span>
          <span class="text-[#666] ml-8">
            {{ authStore.i18n("cm_order.orderTime") }}</span
          ><span>{{ timeFormatByZone(pageData.orderTime) }}</span>
        </div>
        <n-scrollbar
          trigger="none"
          x-scrollable
          class="w-[1240px] overflow-x-auto"
        >
          <table class="inquiry-table">
            <thead>
              <tr>
                <!-- 序号 -->
                <th>{{ authStore.i18n("cm_order.boxNo") }}</th>
                <!-- 商品名称 -->
                <th>{{ authStore.i18n("cm_order.goodsName") }}</th>
                <!-- sku图片 -->
                <th>{{ authStore.i18n("cm_order.skuImageUrl") }}</th>
                <!-- SKU号 -->
                <th>{{ authStore.i18n("cm_order.skuNo") }}</th>
                <!-- 规格 -->
                <th>{{ authStore.i18n("cm_order.specification") }}</th>
                <!-- 价格单位 -->
                <th>{{ authStore.i18n("cm_order.goodsPriceUnitName") }}</th>
                <!-- 下单数量 -->
                <th>{{ authStore.i18n("cm_order.skuCount") }}</th>
                <!-- 销售单价 -->
                <th>
                  {{ authStore.i18n("cm_order.skuUnitPrice") }}
                  <span class="text-xs"> ({{ monetaryUnit }})</span>
                </th>
                <!-- 到手单价 -->
                <th
                  v-if="
                    !isEmptyObject(pageData.currentTransport) &&
                    pageData.transportAmountType === 1
                  "
                >
                  {{ authStore.i18n("cm_order.receivedUnitPrice") }}
                  <span class="text-xs"> ({{ monetaryUnit }})</span>
                  <n-popover trigger="hover">
                    <template #trigger>
                      <icon-card
                        name="mingcute:question-fill"
                        size="20"
                        color="#e50113"
                        class="ml-0.5"
                      >
                      </icon-card>
                    </template>
                    <div class="w-[200px]">
                      {{ authStore.i18n("cm_order.receivedUnitPriceTip") }}
                    </div>
                  </n-popover>
                </th>
                <!-- 总价格(美元) -->
                <th>
                  {{ authStore.i18n("cm_order.skuTotalAmount")
                  }}<span class="text-xs"> ({{ monetaryUnit }})</span>
                </th>
                <!-- 装箱数 -->
                <th>{{ authStore.i18n("cm_order.boxSkuCount") }}</th>
                <!-- 箱数 -->
                <th>{{ authStore.i18n("cm_order.boxCount") }}</th>
                <!-- 箱运费 -->
                <th
                  v-if="
                    !isEmptyObject(pageData.currentTransport) &&
                    pageData.transportAmountType === 1
                  "
                >
                  {{ authStore.i18n("cm_order.boxAmount")
                  }}<span class="text-xs"> ({{ monetaryUnit }})</span>
                </th>
              </tr>
            </thead>
            <tbody
              class="text-center"
              v-for="(box, boxIndex) in pageData.boxList"
              :key="box.boxId"
            >
              <tr v-for="(sku, index) in box.skuList" :key="index">
                <!-- 箱序号 -->
                <td
                  class="min-w-[50px]"
                  v-if="index === 0"
                  :rowspan="box.skuList.length"
                >
                  {{ boxIndex + 1 }}
                </td>
                <!-- 商品名称 -->
                <td class="min-w-[220px] max-w-[320px] text-left">
                  <a
                    :href="`/goods/${sku.goodsId}${
                      sku.padc ? `?padc=${sku.padc}` : ''
                    }`"
                    target="_blank"
                    :data-spm-index="index + 1"
                    data-spm-box="order-detail-goods"
                  >
                    <n-ellipsis
                      :line-clamp="3"
                      :tooltip="false"
                      class="text-blue cursor-pointer break-all"
                    >
                      {{ sku.goodsName }}</n-ellipsis
                    >
                  </a>
                </td>
                <!-- sku图片  -->
                <td class="min-w-[90px]">
                  <n-image
                    object-fit="fill"
                    class="w-[60px] h-[60px] shrink-0 cursor-pointer"
                    :src="sku.picUrl"
                    :img-props="{ referrerpolicy: 'no-referrer' }"
                  />
                </td>
                <!-- SKU号 -->
                <td class="min-w-[150px]">{{ sku.skuNo }}</td>
                <!-- 规格 -->
                <td class="text-left min-w-[180px]">
                  <div
                    v-for="(spec, specIndex) in sku.specList"
                    :key="specIndex"
                  >
                    <div>{{ spec.specName }}:{{ spec.itemName }}</div>
                  </div>
                </td>
                <!-- 价格单位 -->
                <td>{{ sku.goodsPriceUnitName }}</td>
                <!-- 下单数量 -->
                <td class="min-w-[90px]">{{ sku.count }}</td>
                <!-- 销售单价 -->
                <td class="min-w-[80px]">{{ sku.unitPrice }}</td>
                <!-- 到手单价 -->
                <td
                  class="min-w-[140px]"
                  v-if="
                    !isEmptyObject(pageData.currentTransport) &&
                    pageData.transportAmountType === 1
                  "
                >
                  {{ sku.receivedUnitPrice }}
                </td>
                <!-- 总价格(美元) -->
                <td class="min-w-[120px]">{{ sku.totalAmount }}</td>
                <!-- 装箱数 -->
                <td
                  class="min-w-[90px]"
                  v-if="index === 0"
                  :rowspan="box.skuList.length"
                >
                  {{ box.skuCount }}
                </td>
                <!-- 箱数 -->
                <td
                  class="min-w-[90px]"
                  v-if="index === 0"
                  :rowspan="box.skuList.length"
                >
                  {{ box.boxCount }}
                </td>
                <!-- 箱运费 -->
                <td
                  class="min-w-[140px]"
                  v-if="
                    index === 0 &&
                    !isEmptyObject(pageData.currentTransport) &&
                    pageData.transportAmountType === 1
                  "
                  :rowspan="box.skuList.length"
                >
                  {{ box.boxFee }}
                </td>
              </tr>
            </tbody>
          </table>
        </n-scrollbar>
      </div>
      <!-- 备注（客户备注、商家备注）+ 订单费用明细 -->
      <div class="flex my-3 min-h-[320px]">
        <div class="flex-1 bg-white rounded-lg p-5 mr-10 flex">
          <!-- 商家备注 -->
          <div class="flex-1 mr-3" v-if="pageData.merChantRemark">
            <div class="text-lg font-medium mb-3">
              {{ authStore.i18n("cm_order.merchantRemark") }}
            </div>
            <n-scrollbar
              class="border border-[#d7d7d7] py-[6px] px-[8px] rounded-[2px] h-[260px]"
            >
              <span class="whitespace-pre-line">{{
                pageData.merChantRemark
              }}</span>
            </n-scrollbar>
          </div>
          <!-- 客户备注 -->
          <div class="flex-1">
            <div class="text-lg font-medium mb-3">
              {{ authStore.i18n("cm_order.customerRemark") }}
            </div>
            <n-input
              :rows="11"
              clearable
              v-if="isPayFee"
              type="textarea"
              maxlength="1000"
              v-model:value="pageData.orderRemark"
              :on-blur="onOrderRemarkTrack"
              :placeholder="authStore.i18n('cm_order.orderRemarkInput')"
            />
            <n-scrollbar
              v-else
              class="border border-[#d7d7d7] py-[6px] px-[8px] rounded-[2px] h-[260px]"
            >
              <span>{{ pageData.orderRemark }}</span>
            </n-scrollbar>
          </div>
        </div>
        <!-- 订单费用明细 -->
        <div class="flex-1 bg-white rounded-lg py-5">
          <div class="text-[18px] leading-[18px] font-medium px-5 mb-[20px]">
            {{ authStore.i18n("cm_order.orderFeeDetails") }}
          </div>
          <!-- 后台返回的费用展示 -->
          <div>
            <div
              class="text-base font-medium mb-3 flex justify-between px-5 pb-[10px] border-b-1 border-[#D7D7D7]"
            >
              <span>{{ authStore.i18n("cm_order.productCost") }}:</span>
              <span v-if="pageData.productAmount?.amount"
                >{{ setUnit(pageData.productAmount?.amount) }}
              </span>
            </div>
            <n-space vertical :style="{ gap: '8px 0' }">
              <div
                class="border-[#A9A9A9]"
                v-for="(fee, feeIndex) in pageData.productAmount.feeList"
              >
                <div class="px-5 flex justify-between">
                  <span>{{ fee.feeName }}:</span>
                  <span>{{ setUnit(fee.feeAmount) }} </span>
                </div>
                <n-space
                  vertical
                  :style="{ gap: '10px 0' }"
                  class="pl-12 pr-5 py-3"
                  v-if="fee.childFeeList?.length > 0"
                >
                  <div
                    class="flex justify-between"
                    v-for="(childFee, childFeeIndex) in fee.childFeeList"
                    :key="childFeeIndex"
                  >
                    <span>{{ childFee.feeName }}:</span>
                    <span>{{ setUnit(childFee.feeAmount) }}</span>
                  </div>
                </n-space>
              </div>
            </n-space>
          </div>
          <!-- 国际费用 用户选择线路会展示相应的费用 -->
          <div
            v-if="!isEmptyObject(pageData.currentTransport)"
            class="mt-[20px]"
          >
            <div
              class="text-base font-medium my-3 flex justify-between px-5 pb-[10px] border-b-1 border-[#D7D7D7]"
            >
              <span>{{ authStore.i18n("cm_order.interFees") }}：</span>
              <span>{{ setUnit(pageData.currentTransport?.amount) }} </span>
            </div>
            <div class="border-[#A9A9A9]">
              <div class="px-5 flex justify-between">
                <span>{{ authStore.i18n("cm_order.feeAmount") }}</span>
                <span>{{ setUnit(pageData.currentTransport?.amount) }} </span>
              </div>
              <n-space
                vertical
                :style="{ gap: '10px 0' }"
                class="pl-12 pr-5 py-3"
                v-if="pageData.currentTransport?.amountDetailList?.length > 0"
              >
                <div
                  class="flex justify-between"
                  v-for="(childFee, childFeeIndex) in pageData.currentTransport
                    ?.amountDetailList"
                  :key="childFeeIndex"
                >
                  <span>{{ childFee.feeName }}:</span>
                  <span>{{ setUnit(childFee.feeAmount) }}</span>
                </div>
              </n-space>
            </div>
          </div>
        </div>
      </div>
      <!-- 账单费用明细 -->
      <div
        class="flex my-3 min-h-[320px]"
        v-if="
          pageData.mallOrderStatus &&
          pageData.mallOrderStatus !== 'MALL_WAITING_APPROVING' &&
          !isOrderCanceled &&
          !isPayAllFee &&
          !isPayProduct
        "
      >
        <!-- 占位 -->
        <div class="flex-1 rounded-lg p-5 mr-10 flex"></div>
        <div class="flex-1 bg-white rounded-lg py-5">
          <div class="text-[18px] leading-[18px] font-medium px-5 mb-[20px]">
            {{ authStore.i18n("cm_order.paidFeeDetails") }}
          </div>
          <!-- 后台返回的费用展示 -->
          <div>
            <div
              class="text-base font-medium mb-3 flex justify-between px-5 pb-[10px] border-b-1 border-[#D7D7D7]"
            >
              <span>{{ authStore.i18n("cm_order.productCost") }}:</span>
              <span v-if="pageData.productAmount?.amount"
                >{{ setUnit(pageData.productAmount?.amount) }}
              </span>
            </div>
            <n-space vertical :style="{ gap: '8px 0' }">
              <div
                class="border-[#A9A9A9]"
                v-for="(fee, feeIndex) in pageData.productAmount.feeList"
              >
                <div class="px-5 flex justify-between">
                  <span>{{ fee.feeName }}:</span>
                  <span>{{ setUnit(fee.feeAmount) }} </span>
                </div>
                <n-space
                  vertical
                  :style="{ gap: '10px 0' }"
                  class="pl-12 pr-5 py-3"
                  v-if="fee.childFeeList?.length > 0"
                >
                  <div
                    class="flex justify-between"
                    v-for="(childFee, childFeeIndex) in fee.childFeeList"
                    :key="childFeeIndex"
                  >
                    <span>{{ childFee.feeName }}:</span>
                    <span>{{ setUnit(childFee.feeAmount) }}</span>
                  </div>
                </n-space>
              </div>
            </n-space>
          </div>
          <!-- 国际费用 用户选择线路会展示相应的费用 -->
          <div
            v-if="
              !isEmptyObject(pageData.currentTransport) && !hideInterFeeDisplay
            "
            class="mt-[20px]"
          >
            <div
              class="text-base font-medium my-3 flex justify-between px-5 pb-[10px] border-b-1 border-[#D7D7D7]"
            >
              <span>{{ authStore.i18n("cm_order.interFees") }}：</span>
              <span>{{ setUnit(pageData.currentTransport?.amount) }} </span>
            </div>
            <div class="border-[#A9A9A9]">
              <div class="px-5 flex justify-between">
                <span>{{ authStore.i18n("cm_order.feeAmount") }}</span>
                <span>{{ setUnit(pageData.currentTransport?.amount) }} </span>
              </div>
              <n-space
                vertical
                :style="{ gap: '10px 0' }"
                class="pl-12 pr-5 py-3"
                v-if="pageData.currentTransport?.amountDetailList?.length > 0"
              >
                <div
                  class="flex justify-between"
                  v-for="(childFee, childFeeIndex) in pageData.currentTransport
                    ?.amountDetailList"
                  :key="childFeeIndex"
                >
                  <span>{{ childFee.feeName }}:</span>
                  <span>{{ setUnit(childFee.feeAmount) }}</span>
                </div>
              </n-space>
            </div>
          </div>
          <!-- 优惠券明细（支付成功后，用户优惠券明细展示） 线下支付不展示优惠券明细-->
          <div
            class="mt-[20px]"
            v-if="pageData.productAddComissSumAmount && !isPayOffline"
          >
            <div
              class="text-base font-medium my-3 flex justify-between px-5 pb-[10px] border-b-1 border-[#D7D7D7]"
            >
              <span>{{ authStore.i18n("cm_order.coupon") }}:</span>
              <span>{{ setUnit(pageData.productAddComissSumAmount) }} </span>
            </div>
            <div class="border-[#A9A9A9]">
              <n-space vertical :style="{ gap: '8px 0' }">
                <coupon-detail
                  v-if="pageData.productCouponAmount.amount"
                  :title="authStore.i18n('cm_coupon.productCoupons')"
                  :amount="pageData.productCouponAmount.amount"
                  :couponList="pageData.productCouponAmount.productCouponList"
                ></coupon-detail>
                <coupon-detail
                  v-if="pageData.comissCouponAmount.amount"
                  :title="authStore.i18n('cm_coupon.commissionCoupons')"
                  :amount="pageData.comissCouponAmount.amount"
                  :couponList="pageData.comissCouponAmount.commissionCouponList"
                ></coupon-detail>
              </n-space>
            </div>
          </div>
          <!-- 实付款 -->
          <div class="flex justify-between mt-[20px] px-5 text-lg font-medium">
            <span>{{ authStore.i18n("cm_order.actualPaymentTotal") }}:</span>
            <span class="text-[#E50113]"
              >{{ setUnit(pageData.paidAmount) }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="page-footer" id="page-footer" v-if="isPayFee">
      <!-- 线下支付 || 支付国际运费 -->
      <div
        v-if="isPayOffline || isPayInterFee"
        class="flex justify-between items-start"
      >
        <div class="mr-[80px] w-full">
          <!-- 【支付产品成本，支付订单费用】展示数量 -->
          <div
            v-if="isPayAllFee || isPayProduct"
            class="flex justify-between items-center pb-[10px]"
          >
            <span>{{ authStore.i18n("cm_order.productQuantity") }}</span>
            <span class="ml-[30px] text-[16px] leading-[16px]">{{
              pageData.totalCount
            }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-[16px] leading-[16px]">{{
              authStore.i18n("cm_order.amountToPay")
            }}</span>
            <span class="text-[18px] leading-[18px] text-[#e50113]">{{
              setUnit(pageData.actualPaymentAmount)
            }}</span>
          </div>
          <div
            class="text-base text-[#999] flex items-center"
            v-if="pageData.paymentAmountMessage"
          >
            <icon-card
              size="16"
              name="mingcute:warning-line"
              color="#999"
              class="mr-[1px]"
            ></icon-card>
            {{ pageData.paymentAmountMessage.toLowerCase() }}
          </div>
        </div>
        <div>
          <div class="flex items-center text-[14px] leading-[14px] mb-[14px]">
            <n-checkbox
              size="medium"
              class="mr-[6px] bg-[#F2F2F2] agree-checkbox"
              v-model:checked="pageData.acceptTerms"
              :on-update:checked="onUpdateAcceptTerms"
            >
            </n-checkbox>
            <span class="text-[#666] mr-[4px]">{{
              authStore.i18n("cm_order.readAgree")
            }}</span>
            <span
              @click="onOpenAgreeModal"
              class="font-medium text-[#206CCF] hover:underline cursor-pointer"
              >{{ authStore.i18n("cm_news.termsOfService") }}
            </span>
          </div>
          <div class="flex">
            <n-button
              color="#F6D2D4"
              text-color="#E50013"
              :disabled="!!pageData.preview"
              class="rounded-[4px] w-[246px] h-[36px] text-[16px] mr-[28px]"
              @click="onOrderCancel"
            >
              {{ authStore.i18n("cm_order.orderCancel") }}
            </n-button>
            <n-button
              :color="isPayOffline ? '#E6E6E6' : '#E50113'"
              :text-color="isPayOffline ? '#000' : '#fff'"
              :disabled="isPayOffline || !pageData.supportOnlinePay"
              class="rounded-[8px] w-[246px] h-10 text-[16px]"
              data-spm-box="order-detail-go-pay"
              @click="onOrderPay"
            >
              {{ authStore.i18n("cm_order.orderPay") }}
            </n-button>
          </div>
          <div
            @click="onWhatsAppClick"
            v-if="isPayOffline || !pageData.supportOnlinePay"
            class="mt-1 text-[14px] text-[#e50113] cursor-pointer"
          >
            <icon-card
              size="18"
              name="mingcute:warning-line"
              color="#e50113"
              class="mr-1"
            ></icon-card
            >{{ authStore.i18n("cm_order.orderPayTip") }}
            <img
              alt="whatsapp"
              class="inline-block w-[18px] ml-[4px]"
              src="@/assets/icons/common/whatsapp.svg"
            />
          </div>
        </div>
      </div>
      <div v-else class="flex justify-between items-start">
        <div class="mr-[80px] w-full">
          <div class="flex justify-between items-center">
            <span>{{ authStore.i18n("cm_order.totalPaymentAmount") }}</span
            ><span class="text-[16px] leading-[16px] ml-4"
              >{{ setUnit(pageData.paymentAmount) }}
            </span>
          </div>
          <div
            class="text-base text-[#999] flex items-center"
            v-if="pageData.paymentAmountMessage"
          >
            <icon-card
              size="16"
              name="mingcute:warning-line"
              color="#999"
              class="mr-[1px]"
            ></icon-card>
            {{ pageData.paymentAmountMessage.toLowerCase() }}
          </div>
          <!-- 优惠券 -->
          <div
            class="mt-[8px]"
            v-if="(isPayAllFee || isPayProduct) && !isPayOffline"
          >
            <div class="flex justify-between items-center">
              <span>{{ authStore.i18n("cm_order.coupon") }}:</span>
              <span class="text-[#E50013]">
                {{ setUnit(pageData.discountedAmount) }}
              </span>
            </div>
            <div>
              <!-- 产品券 -->
              <div>
                <div
                  class="flex justify-between items-center"
                  v-if="pageData.couponInfo?.productCouponList?.length"
                >
                  <div
                    class="flex-1 flex justify-between items-center py-[6px] px-[14px] text-[#FF4056] bg-[#FEF7F8] rounded-[100px] mr-[17px]"
                  >
                    <div>{{ authStore.i18n("cm_coupon.productCoupons") }}</div>
                    <div v-if="pageData.couponInfo.productCouponAmount">
                      {{ setUnit(pageData.couponInfo.productCouponAmount) }}
                    </div>
                    <div v-else>
                      {{ authStore.i18n("cm_order.youHaveCoupon") }}
                      {{ pageData.couponInfo?.productCouponList?.length }}
                      {{ authStore.i18n("cm_order.availableCoupons") }}
                    </div>
                  </div>
                  <div
                    class="py-[6px] px-[27px] text-[14px] leading-[14px] text-[#FF4056] border border-[#FF94A8] bg-[#FEF7F8] rounded-[100px] cursor-pointer"
                    @click="onChooseCoupon('COUPON_TYPE_PRODUCT')"
                  >
                    {{ authStore.i18n("cm_order.couponChoose") }}
                  </div>
                </div>
                <div
                  v-else
                  class="flex justify-between items-center text-[#7F7F7F]"
                >
                  <div
                    class="flex-1 flex justify-between items-center py-[6px] px-[14px] bg-[#FAFAFA] rounded-[100px] mr-[17px]"
                  >
                    <div>{{ authStore.i18n("cm_coupon.productCoupons") }}</div>
                    <div>
                      {{ authStore.i18n("cm_order.noAvailableCoupons") }}
                    </div>
                  </div>
                  <div
                    class="py-[6px] px-[27px] text-[14px] leading-[14px] bg-[#FAFAFA] rounded-[100px]"
                  >
                    {{ authStore.i18n("cm_order.couponChoose") }}
                  </div>
                </div>
              </div>
              <!-- 佣金券 -->
              <div class="mt-[2px]">
                <div
                  class="flex justify-between items-center"
                  v-if="pageData.couponInfo?.commissionCouponList?.length"
                >
                  <div
                    class="flex-1 flex justify-between items-center py-[6px] px-[14px] text-[#9C4A2C] bg-[#FEFAF7] rounded-[100px] mr-[17px]"
                  >
                    <div>
                      {{ authStore.i18n("cm_coupon.commissionCoupons") }}
                    </div>
                    <div v-if="pageData.couponInfo.commissionCouponAmount">
                      {{ setUnit(pageData.couponInfo.commissionCouponAmount) }}
                    </div>
                    <div v-else>
                      {{ authStore.i18n("cm_order.youHaveCoupon") }}
                      {{ pageData.couponInfo?.commissionCouponList?.length }}
                      {{ authStore.i18n("cm_order.availableCoupons") }}
                    </div>
                  </div>
                  <div
                    class="py-[6px] px-[27px] text-[14px] leading-[14px] text-[#9C4A2C] border border-[#FFC194] bg-[#FEFAF7] rounded-[100px] cursor-pointer"
                    @click="onChooseCoupon('COUPON_TYPE_COMMISSION')"
                  >
                    {{ authStore.i18n("cm_order.couponChoose") }}
                  </div>
                </div>
                <div
                  v-else
                  class="flex justify-between items-center text-[#7F7F7F]"
                >
                  <div
                    class="flex-1 flex justify-between items-center py-[6px] px-[14px] bg-[#FAFAFA] rounded-[100px] mr-[17px]"
                  >
                    <div>
                      {{ authStore.i18n("cm_coupon.commissionCoupons") }}
                    </div>
                    <div>
                      {{ authStore.i18n("cm_order.noAvailableCoupons") }}
                    </div>
                  </div>
                  <div
                    class="py-[6px] px-[27px] text-[14px] leading-[14px] bg-[#FAFAFA] rounded-[100px]"
                  >
                    {{ authStore.i18n("cm_order.couponChoose") }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="flex justify-between items-center pb-[10px]"
            v-if="isPayAllFee || isPayProduct"
          >
            <span>{{ authStore.i18n("cm_order.productQuantity") }}</span>
            <span class="ml-[30px] text-[16px] leading-[16px]">{{
              pageData.totalCount
            }}</span>
          </div>
          <div class="flex justify-between items-center pb-[14px]">
            <span class="text-[16px] leading-[16px]">{{
              authStore.i18n("cm_order.amountToPay")
            }}</span>
            <span class="text-[18px] leading-[18px] text-[#e50113]">{{
              setUnit(pageData.actualPaymentAmount)
            }}</span>
          </div>
          <div class="flex items-center text-[14px] leading-[14px] my-[14px]">
            <n-checkbox
              size="medium"
              class="mr-[6px] bg-[#F2F2F2] agree-checkbox"
              v-model:checked="pageData.acceptTerms"
              :on-update:checked="onUpdateAcceptTerms"
            >
            </n-checkbox>
            <span class="text-[#666] mr-[4px]">{{
              authStore.i18n("cm_order.readAgree")
            }}</span>
            <span
              @click="onOpenAgreeModal"
              class="font-medium text-[#206CCF] hover:underline cursor-pointer"
              >{{ authStore.i18n("cm_news.termsOfService") }}
            </span>
          </div>
          <div class="flex">
            <n-button
              color="#F6D2D4"
              text-color="#E50013"
              :disabled="!!pageData.preview"
              class="rounded-[4px] w-[246px] h-[36px] text-[16px] mr-[28px]"
              @click="onOrderCancel"
            >
              {{ authStore.i18n("cm_order.orderCancel") }}
            </n-button>
            <n-button
              :color="isPayOffline ? '#E6E6E6' : '#E50113'"
              :text-color="isPayOffline ? '#000' : '#fff'"
              :disabled="isPayOffline || !pageData.supportOnlinePay"
              class="rounded-[8px] w-[246px] h-10 text-[16px]"
              data-spm-box="order-detail-go-pay"
              @click="onOrderPay"
            >
              {{ authStore.i18n("cm_order.orderPay") }}
            </n-button>
          </div>
          <div
            @click="onWhatsAppClick"
            v-if="!pageData.supportOnlinePay"
            class="mt-1 text-[14px] text-[#e50113] cursor-pointer"
          >
            <icon-card
              size="18"
              name="mingcute:warning-line"
              color="#e50113"
              class="mr-1"
            ></icon-card
            >{{ authStore.i18n("cm_order.orderPayTip") }}
            <img
              alt="whatsapp"
              class="inline-block w-[18px] ml-[4px]"
              src="@/assets/icons/common/whatsapp.svg"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- <n-modal
    preset="dialog"
    :block-scroll="true"
    :show-icon="false"
    v-model:show="pageData.showLogDetails"
    title="物流详情"
  >
    <div class="flex flex-col items-center mt-6">
      <n-timeline>
        <n-timeline-item
          color="#e50113"
          title="重新派送"
          content="包裹正在等待重新派送"
          time="2024-08-23 16:00"
          line-type="dashed"
        >
          <template #icon>
            <icon-card
              name="lets-icons:check-fill"
              size="28"
              color="#E50113"
              class="z-10"
            >
            </icon-card>
          </template>
        </n-timeline-item>
        <n-timeline-item
          content="物流单创建"
          time="2024-08-23 10:00"
          line-type="dashed"
        />
        <n-timeline-item
          title="已发货"
          content="包裹已成功发出"
          time="2024-08-23 12:30"
          line-type="dashed"
        />
        <n-timeline-item
          title="派送失败"
          content="因地址问题，派送失败"
          time="2024-08-23 15:00"
          line-type="dashed"
        />

        <n-timeline-item
          title="已完成"
          content="包裹已成功送达"
          time="2024-08-23 18:00"
          line-type="dashed"
        />
        <n-timeline-item content="感谢使用我们的服务" />
      </n-timeline>
    </div>
  </n-modal> -->

  <!-- 为什么估计装运 -->
  <n-modal
    preset="dialog"
    :block-scroll="true"
    :show-icon="false"
    v-model:show="pageData.showLoadTrans"
    :title="authStore.i18n('cm_order.WETrans')"
    :style="{
      width: '640px',
      padding: '20px 40px',
    }"
  >
    <div class="flex flex-col items-center mt-6">
      <n-space vertical :style="{ gap: '14px 0' }">
        <div>
          {{ authStore.i18n("cm_order.transQuotation") }}
        </div>
        <div>
          {{ authStore.i18n("cm_order.betterTrans") }}
        </div>
        <div>
          {{ authStore.i18n("cm_order.transService") }}
        </div>
      </n-space>

      <n-button
        color="#E50113"
        text-color="#fff"
        @click="pageData.showLoadTrans = false"
        class="rounded-[8px] h-8 text-[16px] mt-10"
      >
        {{ authStore.i18n("cm_order.transKnown") }}
      </n-button>
    </div>
  </n-modal>

  <!-- 点击付款 付款信息错误提示 -->
  <n-modal
    preset="dialog"
    :block-scroll="true"
    :show-icon="false"
    v-model:show="pageData.showPayInfoError"
    :style="{
      width: '400px',
      padding: '20px 40px',
    }"
    :on-close="onClosePayError"
    :on-esc="onClosePayError"
    :on-mask-click="onClosePayError"
  >
    <div class="text-center">
      <icon-card
        size="22"
        name="mingcute:warning-line"
        color="#e50113"
        class="mr-1"
      >
      </icon-card>
      {{ authStore.i18n("cm_order.refreshPage") }}
    </div>
  </n-modal>

  <!-- 点击付款 优惠券信息错误提示 -->
  <n-modal
    preset="dialog"
    :show-icon="false"
    :block-scroll="true"
    v-model:show="pageData.showCouponInfoError"
    :style="{
      width: '500px',
      padding: '30px 40px',
    }"
    :closable="false"
    :closeOnEsc="false"
    :maskClosable="false"
  >
    <div class="text-center">
      <div>
        <icon-card
          size="20"
          color="#e50113"
          name="mingcute:warning-line"
        ></icon-card>
        {{ authStore.i18n("cm_order.chooseCouponError") }}
      </div>

      <n-button
        color="#E50113"
        text-color="#fff"
        @click="onChooseCouponAgain"
        class="rounded-[50px] mt-[20px]"
      >
        {{ authStore.i18n("cm_order.chooseCouponAgain") }}
      </n-button>
    </div>
  </n-modal>

  <!-- 优惠券选择 -->
  <n-modal
    preset="dialog"
    :closable="false"
    :show-icon="false"
    :autoFocus="false"
    :block-scroll="true"
    v-model:show="pageData.showCouponChoose"
    :style="{
      width: '570px',
      height: '482px',
      padding: '22px 10px 16px 20px',
    }"
  >
    <div class="text-[20px] leading-[20px] font-medium text-center">
      {{
        pageData.chooseCouponType === "COUPON_TYPE_PRODUCT"
          ? authStore.i18n("cm_coupon.productCoupons")
          : authStore.i18n("cm_coupon.commissionCoupons")
      }}
    </div>
    <n-scrollbar class="mt-[20px] h-[300px] pr-[16px]">
      <div v-show="pageData.chooseCouponLoading" class="loading-overlay">
        <n-spin stroke="#e50113" :show="pageData.chooseCouponLoading"> </n-spin>
      </div>
      <n-checkbox-group
        v-model:value="pageData.selectedCouponIds"
        @update:value="onCheckAvailableList"
        class="flex flex-col items-center w-full coupon-wrapper"
      >
        <n-checkbox
          class="w-full coupon-card pb-[8px] flex items-center"
          v-for="(coupon, index) in pageData.chooseCouponList"
          :key="coupon.id"
          :value="coupon.id"
          :disabled="
            coupon?.availableFlag === false ||
            coupon?.ticketStatus === 'TICKET_LOCK'
          "
          :class="{
            'mb-[8px] border-b border-[#F2F2F2]':
              index !== pageData.chooseCouponList.length - 1,
            'checked-coupon': coupon?.check,
            'disabled-coupon': coupon?.availableFlag === false,
          }"
        >
          <div class="flex-1 flex items-center">
            <div
              class="coupon-border py-[14px] border-[2px] border-dotted w-[102px] text-center text-[18px] leading-[18px] font-medium"
            >
              <span v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
                {{ discountToPercentage(coupon.discount) }}
                {{ authStore.i18n("cm_coupon.discount").toLowerCase() }}
              </span>
              <span v-else>
                {{ setNewUnit(coupon?.preferentialAmount) }}
              </span>
            </div>
            <div class="flex-1 ml-[13px] mr-[16px]">
              <div class="text-[16px] leading-[16px] font-medium">
                <!-- 满多少金额使用 -->
                <template v-if="coupon?.couponUseConditionsType === 'FULL'">
                  <span>
                    {{ authStore.i18n("cm_coupon.minimumRequired") }}
                  </span>
                  {{ setNewUnit(coupon?.useConditionsAmount) }}
                </template>
                <!-- 每满多少金额使用 -->
                <template
                  v-if="coupon?.couponUseConditionsType === 'EVERY_FULL'"
                >
                  <span>
                    {{ authStore.i18n("cm_coupon.minimumUnmet") }}
                  </span>
                  {{ setNewUnit(coupon?.useConditionsAmount) }}
                  <span>
                    {{ authStore.i18n("cm_coupon.minimumUnmetCost") }}
                  </span>
                </template>
                <!-- 不限制金额 -->
                <div v-if="coupon?.couponUseConditionsType === 'UNLIMITED'">
                  {{ authStore.i18n("cm_coupon.noLimit") }}
                </div>
              </div>
              <div
                v-if="
                  coupon?.couponWay === 'COUPON_WAY_DISCOUNT' &&
                  coupon?.preferentialAmount
                "
                class="text-[14px] leading-[14px] flex items-center mt-[6px]"
              >
                {{ authStore.i18n("cm_coupon.upToMoney") }}
                {{ setNewUnit(coupon?.preferentialAmount) }}
              </div>
              <div
                v-if="coupon?.availableFlag === false"
                class="text-[14px] leading-[14px] flex items-center mt-[6px]"
              >
                <img
                  loading="lazy"
                  src="@/assets/icons/tipGray.svg"
                  alt="tip"
                  class="w-[14px] mr-[2px]"
                  referrerpolicy="no-referrer"
                />
                {{ coupon?.notAvailableReason }}
              </div>
            </div>
          </div>
        </n-checkbox>
      </n-checkbox-group>
    </n-scrollbar>
    <div class="my-[18px] flex justify-between items-center">
      <span class="text-[16px] leading-[16px] font-medium"
        >{{ authStore.i18n("cm_order.existingDiscount") }}:</span
      >
      <span class="text-[20px] leading-[20px] text-[#e50113]">{{
        setUnit(pageData.chooseCouponAmount)
      }}</span>
    </div>
    <div class="flex justify-between items-center">
      <n-button
        color="#F6D2D4"
        text-color="#E50113"
        @click="onChooseCouponCancel"
        class="rounded-[8px] w-[200px] h-[38px] text-[16px]"
      >
        {{ authStore.i18n("cm_order.orderCancel") }}
      </n-button>
      <n-button
        color="#E50113"
        text-color="#fff"
        @click="onChooseCouponConfirm"
        class="rounded-[8px] w-[200px] h-[38px] text-[16px]"
      >
        {{ authStore.i18n("cm_order.orderConfirm") }}
      </n-button>
    </div>
  </n-modal>

  <!-- 订单取消 -->
  <order-cancel
    ref="orderCancelRef"
    @updateOrderState="onUpdateOrderState"
  ></order-cancel>

  <!-- 订单支付协议 -->
  <order-pay-agree
    ref="orderPayAgreeRef"
    @onHasReadFinished="onHasReadFinished"
    @onUpdateAcceptTerms="onUpdateAcceptTerms"
  >
  </order-pay-agree>
</template>

<script setup lang="ts">
import _ from "lodash";
import { useAuthStore } from "@/stores/authStore";
import OrderStep from "./components/OrderStep.vue";
import OrderCancel from "@/pages/order/components/OrderCancel.vue";
import CouponDetail from "@/pages/order/components/CouponDetail.vue";
import OrderPayAgree from "@/pages/order/components/OrderPayAgree.vue";

const route = useRoute();
const authStore = useAuthStore();
const orderCancelRef = ref<any>(null);
const orderPayAgreeRef = ref<any>(null);
const pageData = reactive(<any>{
  orderNo: route?.query?.orderNo || "", //订单号
  preview: route?.query?.preview || false, //是否来自admin的预览
  mallOrderStatus: null, // 订单状态
  statusDesc: null, // 订单状态描述
  orderTime: null, // 下单时间
  orderRemark: "", // 客户备注
  merChantRemark: "", //商家备注
  payType: "", // 支付方式 (线上支付或线下支付)
  payMode: "", // 支付模式 (一次性支付或分开支付)
  quotationMode: null, //报价模式
  addressInfo: <any>{}, // 地址信息
  boxList: <any>[], // 装箱列表
  totalCount: null, //sku总数量
  totalAmount: null, //sku总价值
  transportAmountList: <any>[], //运输线路与预估费用列表
  transportId: null, //选中的运输线路id
  currentTransport: {}, //当前选中的运输线路 需根据这个线路计算 装箱信息的箱运费、到手单价以及订单总价
  expandedTransport: [], // 默认展开的运输线路列表
  showLoadTrans: false, // 是否展示估计装运的介绍
  showLogDetails: false, // 是否展示物流信息
  showPayInfoError: false, // 是否展示付款信息错误提示
  productAmount: <any>{}, //产品成本
  paymentAmount: null, //订单总价 前端根据报价模式 支付模式计算
  paymentAmountMessage: null, // 订单总价提示
  actualPaymentAmount: 0, // 实际支付金额(订单总价-优惠金额)
  couponInfo: <any>{
    productCouponAmount: 0, //产品优惠的金额
    productCouponIds: <any>[], //产品优惠券id列表
    productCouponList: <any>[], //产品优惠券列表
    commissionCouponAmount: 0, //佣金优惠的金额
    commissionCouponIds: [], //佣金优惠券id列表
    commissionCouponList: <any>[], //佣金优惠券列表
  }, //优惠券信息（包含产品和佣金优惠券列表及对应的优惠金额）
  discountedAmount: 0, // 优惠金额
  showCouponChoose: false,
  chooseCouponType: null, // 当前弹窗打开的优惠券类型
  chooseCouponList: null, // 选择的优惠券列表
  chooseCouponAmount: 0, // 选择的优惠券列表里选中的优惠券优惠金额的总计
  selectedCouponIds: [],
  chooseCouponLoading: false,
  allCouponError: false, //产品/佣金都错误
  productCouponError: false, //产品券信息错误
  commissionCouponError: false, // 佣金券信息错误
  showCouponInfoError: false, //点击付款 是否展示优惠券信息错误提示

  productCouponAmount: <any>{}, // 产品券费用明细
  comissCouponAmount: <any>{}, // 佣金券费用明细
  productAddComissSumAmount: null, //付款成功后的产品券+佣金券优惠总金额
  paidAmount: null, //实付款
  hasReadFinished: false, // 是否阅读完支付协议
  acceptTerms: false, // 是否确认协议
  sourcePlatform: null, // 订单来源平台
  supportOnlinePay: true, // 是否支持在线支付
});

const isOrderCanceled = computed(() => {
  return pageData.mallOrderStatus === "MALL_CANCELED";
});

// 线下支付
const isPayOffline = computed(() => {
  return pageData.payType === "ONLINE_PAY_OFFLINE";
});

// 待支付订单费用
const isPayAllFee = computed(() => {
  return pageData.mallOrderStatus === "MALL_WAIT_PAY_ALL_FEE";
});

// 待支付产品成本
const isPayProduct = computed(() => {
  return pageData.mallOrderStatus === "MALL_WAIT_PAY_PRODUCT";
});

// 待支付国际费用
const isPayInterFee = computed(() => {
  return pageData.mallOrderStatus === "MALL_WAIT_PAY_INTER_FEE";
});

// 待支付费用
const isPayFee = computed(() => {
  return isPayAllFee.value || isPayProduct.value || isPayInterFee.value;
});

// 用户分开支付，支付产品成本后，已付款明细只展示产品成本，国际费用隐藏
const hideInterFeeDisplay = computed(() => {
  return (
    pageData.payMode === "PAY_MODE_PART" &&
    (pageData.quotationMode === "QUOTATION_MODE_DDP" ||
      pageData.quotationMode === "QUOTATION_MODE_CIF") &&
    (pageData.mallOrderStatus === "MALL_PURCHASING" ||
      pageData.mallOrderStatus === "MALL_WAIT_CAL_INTER_FEE" ||
      pageData.mallOrderStatus === "MALL_WAIT_PAY_INTER_FEE")
  );
});

onMounted(() => {
  window.addEventListener("scroll", onScroll);
  document.documentElement.style.backgroundColor = "#f5f3f3"; // fix bug
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
});

onGetOrderDetail();
async function onGetOrderDetail() {
  const res: any = await useGetOrderDetail({
    orderNo: pageData.orderNo,
    isAdminPreview: pageData.preview || false, //是否来自admin的预览
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    pageData.transportAmountList.sort((a: any, b: any) => a.amount - b.amount);
    pageData.transportAmountList.forEach((item: any) => {
      pageData.expandedTransport.push(item.transportId);
    });

    // 待支付订单请求可用优惠券列表
    if ((isPayAllFee.value || isPayProduct.value) && !isPayOffline.value) {
      onGetCouponList("init");
    }

    onUpdateTrans();
    onInitPaymentAmount();
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

async function onGetCouponList(init?: any) {
  const res: any = await useGetCouponUsableList({ orderNo: pageData.orderNo });
  if (res?.result?.code === 200) {
    if (pageData.productCouponError === true) {
      pageData.couponInfo.productCouponList = res?.data.productCouponList;
      return;
    } else if (pageData.commissionCouponError === true) {
      pageData.couponInfo.commissionCouponList = res?.data.commissionCouponList;
      return;
    }
    pageData.couponInfo = res?.data;
    if (init) {
      onCheckCouponInfo();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res.result?.message);
  }
}

// 校验该订单的优惠券是否被锁定
function onCheckCouponInfo() {
  let isLocked = false; // 标记是否存在锁定状态的优惠券
  const couponLists = [
    {
      list: pageData.couponInfo.productCouponList,
      type: "COUPON_TYPE_PRODUCT",
    },
    {
      list: pageData.couponInfo.commissionCouponList,
      type: "COUPON_TYPE_COMMISSION",
    },
  ];

  // 检查是否存在锁定状态的优惠券
  isLocked = couponLists.some(({ list }) =>
    list.some((coupon: any) => coupon.ticketStatus === "TICKET_LOCK")
  );

  // 如果存在锁定状态，才进行计算
  if (isLocked) {
    couponLists.forEach(({ list, type }) => {
      let totalPreferentialAmount = 0; // 累计优惠金额
      const selectedCouponIds: string[] = [];
      const chooseCouponList: any[] = [];

      // 遍历优惠券，计算选中优惠券的信息
      list.forEach((coupon: any) => {
        if (coupon.check) {
          totalPreferentialAmount += coupon.ticketActualPrice || 0;
          selectedCouponIds.push(coupon.id);
          chooseCouponList.push(_.cloneDeep(coupon));
        }
      });

      // 更新对应的 pageData 数据
      if (type === "COUPON_TYPE_PRODUCT") {
        pageData.couponInfo.productCouponAmount = -totalPreferentialAmount;
        pageData.couponInfo.productCouponIds = _.cloneDeep(selectedCouponIds);
      } else if (type === "COUPON_TYPE_COMMISSION") {
        pageData.couponInfo.commissionCouponAmount = -totalPreferentialAmount;
        pageData.couponInfo.commissionCouponIds =
          _.cloneDeep(selectedCouponIds);
      }
    });
    onUpdatePaymentAmount();
  }
}

// 根据运输线路 计算箱运费、到手单价、订单总价、实际支付金额
// 有运输线路且使用单箱运费模式展示到手单价、箱运费
function onUpdateTrans(val?: any) {
  if (!pageData.transportAmountList.length) return;
  let matchTrans;
  if (val) {
    matchTrans = pageData.transportAmountList.find(
      (item: any) => item.transportId === val
    );
    window?.MyStat?.addPageEvent(
      "payment_choice_route",
      `选择线路：${matchTrans.name}`
    );
    pageData.transportId = val;
  } else {
    matchTrans = pageData.transportAmountList[0];
    pageData.transportId = matchTrans.transportId;
  }

  if (matchTrans) {
    pageData.currentTransport = matchTrans;
    // 更新每个箱子的箱运费
    if (pageData.transportAmountType === 1) {
      pageData.boxList.forEach((box: any) => {
        const matchedFee = box.boxTransportFeeList.find(
          (fee: any) => fee.transportId === matchTrans.transportId
        );
        if (matchedFee) {
          box.boxFee = matchedFee.amount;
        }
        // 更新每个sku的到手单价(销售单价+单个商品分摊的运费，单个商品分摊运费=单箱运费/单箱装箱数，除不尽四舍五入，保留两位小数)
        box.skuList.forEach((sku: any) => {
          const boxFeePerUnit = (box.boxFee * 100) / box.skuCount / 100;
          sku.receivedUnitPrice = (
            Number(boxFeePerUnit) + Number(sku.unitPrice)
          ).toFixed(2);
        });
      });
    }

    //更新订单总价(支付模式为一次性支付时，产品成本 + 国际费用)、实际支付金额
    if (pageData.payMode === "PAY_MODE_ALL") {
      pageData.paymentAmount = mathRound(
        pageData.productAmount?.amount + pageData.currentTransport?.amount
      );
      pageData.actualPaymentAmount = mathRound(
        pageData.paymentAmount + pageData.discountedAmount
      );
    }
  }
}

/**
 * 计算订单总价,实际支付金额
 * 报价模式为 DDP或者 CIF 时
 * 支付模式为 分开支付：
 *    - 支付产品成本时，订单总价为产品成本；提示为（产品成本）
 *    - 支付国际费用时，订单总价为国际费用。提示为（国际费用）
 * 支付模式为 一次性支付时：
 *    - 订单总价为产品成本 + 国际费用。提示为（产品成本 + 国际费用）
 *
 * 其他报价模式：
 *    - 一次性支付时，订单总价为产品成本。提示为（产品成本）
 */
function onInitPaymentAmount() {
  const { payMode, quotationMode, productAmount, currentTransport } = pageData;

  // 报价模式为 DDP 或 CIF 时
  if (
    quotationMode === "QUOTATION_MODE_DDP" ||
    quotationMode === "QUOTATION_MODE_CIF"
  ) {
    // 支付模式为一次性支付时，产品成本 + 国际费用
    if (payMode === "PAY_MODE_ALL") {
      pageData.paymentAmount = mathRound(
        productAmount?.amount + currentTransport?.amount,
        2
      );
      pageData.paymentAmountMessage = `${authStore.i18n(
        "cm_order.productCost"
      )} + ${authStore.i18n("cm_order.interFees")}`;
    } else {
      // 支付模式为分开支付时，根据订单状态决定支付金额
      if (isPayProduct.value) {
        pageData.paymentAmount = productAmount?.amount;
        pageData.paymentAmountMessage = authStore.i18n("cm_order.productCost");
      } else if (isPayInterFee.value) {
        pageData.paymentAmount = currentTransport?.amount;
        pageData.paymentAmountMessage = authStore.i18n("cm_order.interFees");
      }
    }
  } else {
    // 其他报价模式时，支付金额为产品成本
    pageData.paymentAmount = productAmount?.amount;
    pageData.paymentAmountMessage = authStore.i18n("cm_order.productCost");
  }
  // 更新实际支付金额
  pageData.actualPaymentAmount = mathRound(
    pageData.paymentAmount + pageData.discountedAmount
  );
}

function onOrderRemarkTrack() {
  window?.MyStat?.addPageEvent(
    "payment_input_buyer_msg",
    `输入买家留言：${pageData.orderRemark}`
  );
}

// 点击付款 跳转至收银台
async function onOrderPay(event?: any) {
  // 未确认协议，弹出协议弹框
  if (!pageData.acceptTerms) {
    window?.MyStat?.addPageEvent(
      "payment_open_terms",
      `打开支付协议对话框（付款时未勾选协议，弹出协议对话框）`
    ); // 埋点
    orderPayAgreeRef?.value?.onOpenAgree();
    return;
  }

  let paymentId;
  const res: any = await useOpenCashDesk({
    orderNo: pageData.orderNo,
    amount: pageData.actualPaymentAmount,
    orderRemark: pageData.orderRemark,
    transportId: pageData.transportId,
    productIdsList: pageData.couponInfo.productCouponIds,
    commissionIdsList: pageData.couponInfo.commissionCouponIds,
  });
  if (res?.result?.code === 200) {
    paymentId = res?.data?.paymentId;
    navigateToPage(
      "/order/payment",
      {
        orderNo: pageData.orderNo,
        paymentId,
      },
      false,
      event
    );
  } else {
    window?.MyStat?.addPageEvent(
      "order_detail_gopay_fail",
      `订单详情页去支付失败：${res?.result?.message}`
    );
    if (res?.result?.code === 81005) {
      pageData.showPayInfoError = true;
    } else if (res?.result?.code === 70114) {
      //产品券信息错误
      pageData.showCouponInfoError = true;
      pageData.productCouponError = true;
      pageData.couponInfo.productCouponIds = [];
      pageData.couponInfo.productCouponAmount = 0;
      onUpdatePaymentAmount();
      onGetCouponList();
    } else if (res?.result?.code === 70115) {
      //佣金券信息错误
      pageData.showCouponInfoError = true;
      pageData.commissionCouponError = true;
      pageData.couponInfo.commissionCouponIds = [];
      pageData.couponInfo.commissionCouponAmount = 0;
      onUpdatePaymentAmount();
      onGetCouponList();
    } else if (res?.result?.code === 70116) {
      //产品/佣金都错误
      pageData.showCouponInfoError = true;
      pageData.allCouponError = true;
      pageData.couponInfo.productCouponIds = [];
      pageData.couponInfo.productCouponAmount = 0;
      pageData.couponInfo.commissionCouponIds = [];
      pageData.couponInfo.commissionCouponAmount = 0;
      onUpdatePaymentAmount();
      onGetCouponList();
    } else {
      return showToast(res?.result?.message);
    }
  }
}

// 点击付款 报错提示
function onClosePayError() {
  pageData.showPayInfoError = false;
  window.location.reload();
}

// 取消订单
function onOrderCancel() {
  orderCancelRef.value?.onOpenDialog(pageData.orderNo);
}

// 取消订单后, 订单状态更新
function onUpdateOrderState() {
  window.location.reload();
}

// 展示物流信息
function onOpenLogDetail() {
  pageData.showLogDetails = true;
}

// 展示估计装运的介绍
function onOpenLoadTrans() {
  pageData.showLoadTrans = true;
}

// 页面横向滚动时 底部已固定的部分跟随滚动
function onScroll(e?: any) {
  const pageFooter = document.getElementById("page-footer");
  if (!pageFooter) return;

  const scrollLeft =
    document.documentElement.scrollLeft || document.body.scrollLeft || 0;
  pageFooter.style.left = `${-scrollLeft}px`;
}

// 打开优惠券选择弹框
function onChooseCoupon(type: any) {
  // 重置数据
  pageData.selectedCouponIds = [];
  pageData.chooseCouponList = [];
  pageData.chooseCouponAmount = 0;
  pageData.chooseCouponType = type;
  pageData.showCouponChoose = true;

  if (type === "COUPON_TYPE_PRODUCT") {
    pageData.selectedCouponIds = _.cloneDeep(
      pageData.couponInfo.productCouponIds || []
    );
    pageData.chooseCouponList = _.cloneDeep(
      pageData.couponInfo.productCouponList || []
    );
    pageData.chooseCouponAmount = _.cloneDeep(
      pageData.couponInfo.productCouponAmount || 0
    );
  } else if (type === "COUPON_TYPE_COMMISSION") {
    pageData.selectedCouponIds = _.cloneDeep(
      pageData.couponInfo.commissionCouponIds || []
    );
    pageData.chooseCouponList = _.cloneDeep(
      pageData.couponInfo.commissionCouponList || []
    );
    pageData.chooseCouponAmount = _.cloneDeep(
      pageData.couponInfo.commissionCouponAmount || 0
    );
  }
  window?.MyStat?.addPageEvent(
    "order_open_coupon_list",
    `打开${
      pageData.chooseCouponType === "COUPON_TYPE_PRODUCT" ? "产品" : "佣金"
    }优惠券列表`
  ); // 埋点
}

// 重新选择优惠券
function onChooseCouponAgain() {
  pageData.showCouponInfoError = false;
  if (pageData.productCouponError || pageData.allCouponError) {
    onChooseCoupon("COUPON_TYPE_PRODUCT");
  } else {
    onChooseCoupon("COUPON_TYPE_COMMISSION");
  }
}

// 优惠券选择
async function onCheckAvailableList() {
  pageData.chooseCouponLoading = true;
  // 选中的优惠券
  const selectCouponModelsList = pageData.selectedCouponIds.map((id: any) => {
    const coupon = pageData.chooseCouponList.find(
      (coupon: any) => coupon.id === id
    );
    if (coupon && !coupon.check) {
      window?.MyStat?.addPageEvent(
        "order_select_coupon",
        `选择优惠券：${coupon.couponName}`
      ); //埋点
    }
    if (coupon) {
      coupon.check = true; // 标记优惠券为已选中
    }
    return coupon;
  });

  // 未选中的优惠券
  const notCouponModelsList = pageData.chooseCouponList.filter(
    (coupon: any) => {
      const isNotSelected = !pageData.selectedCouponIds.includes(coupon.id);
      if (isNotSelected && coupon.check) {
        window?.MyStat?.addPageEvent(
          "order_unselect_coupon",
          `取消选择优惠券：${coupon.couponName}`
        ); //埋点
      }
      if (isNotSelected) {
        coupon.check = false; // 标记为未选中
      }
      return isNotSelected;
    }
  );

  const res: any = await useCheckAvailableList({
    orderNo: pageData.orderNo,
    selectCouponModelsList,
    notCouponModelsList,
    couponType: pageData.chooseCouponType,
  });
  pageData.chooseCouponLoading = false;
  if (res?.result?.code === 200) {
    let totalDiscountAmount = 0;
    pageData.chooseCouponList.forEach((coupon: any) => {
      const matchedCoupon = res?.data?.find(
        (item: any) => item.id === coupon.id
      );

      if (matchedCoupon) {
        // 更新优惠券的 check, availableFlag, ticketActualPrice
        coupon.check = matchedCoupon.check;
        coupon.availableFlag = matchedCoupon.availableFlag;
        coupon.ticketActualPrice = matchedCoupon.ticketActualPrice;
        coupon.notAvailableReason = matchedCoupon.notAvailableReason;

        // 如果优惠券的check为 true，累计优惠金额
        if (coupon.check) {
          totalDiscountAmount += coupon.ticketActualPrice;
        }

        // 如果优惠券的check为false 且该id在selectedCouponIds中，则从selectedCouponIds中移除该优惠券的 id
        if (!coupon.check && pageData.selectedCouponIds.includes(coupon.id)) {
          pageData.selectedCouponIds = pageData.selectedCouponIds.filter(
            (id: any) => id !== coupon.id
          );
          window?.MyStat?.addPageEvent(
            "order_unselect_coupon",
            `自动取消选择优惠券：${coupon.couponName}`
          ); // 埋点
        }
      }
    });

    pageData.chooseCouponAmount = -totalDiscountAmount;
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res.result?.message);
  }
}

// 优惠券选择确认
function onChooseCouponConfirm() {
  if (pageData.chooseCouponType === "COUPON_TYPE_PRODUCT") {
    pageData.couponInfo.productCouponAmount = pageData.chooseCouponAmount;
    pageData.couponInfo.productCouponList = pageData.chooseCouponList;
    pageData.couponInfo.productCouponIds = pageData.selectedCouponIds;
  } else if (pageData.chooseCouponType === "COUPON_TYPE_COMMISSION") {
    pageData.couponInfo.commissionCouponAmount = pageData.chooseCouponAmount;
    pageData.couponInfo.commissionCouponList = pageData.chooseCouponList;
    pageData.couponInfo.commissionCouponIds = pageData.selectedCouponIds;
  }
  window?.MyStat?.addPageEvent(
    "order_confirm_coupon",
    `确认${
      pageData.chooseCouponType === "COUPON_TYPE_PRODUCT" ? "产品" : "佣金"
    }优惠券，共${pageData.selectedCouponIds.length}张，优惠金额：${setUnit(
      -pageData.chooseCouponAmount
    )}`
  ); // 埋点
  onUpdatePaymentAmount();
  pageData.showCouponChoose = false;
}

// 计算优惠金额,计算实际金额
function onUpdatePaymentAmount() {
  // 计算优惠金额
  pageData.discountedAmount = mathRound(
    (pageData.couponInfo.productCouponAmount || 0) +
      (pageData.couponInfo.commissionCouponAmount || 0)
  );
  // 更新实际支付金额
  pageData.actualPaymentAmount = mathRound(
    pageData.paymentAmount + pageData.discountedAmount
  );
}

function onChooseCouponCancel() {
  window?.MyStat?.addPageEvent(
    "order_close_coupon_list",
    `关闭${
      pageData.chooseCouponType === "COUPON_TYPE_PRODUCT" ? "产品" : "佣金"
    }优惠券列表`
  ); // 埋点
  pageData.showCouponChoose = false;
}

function onOpenAgreeModal() {
  window?.MyStat?.addPageEvent(
    "payment_open_terms",
    `打开支付协议对话框(点击支付协议，弹出协议对话框)`
  ); // 埋点
  orderPayAgreeRef?.value?.onOpenAgree();
}

function onHasReadFinished() {
  pageData.hasReadFinished = true;
}

function onUpdateAcceptTerms(val?: any) {
  // 阅读完协议后，即可确认协议
  if (pageData.hasReadFinished) {
    pageData.acceptTerms = val;
    if (pageData.acceptTerms) {
      window?.MyStat?.addPageEvent("payment_agree_terms", `同意支付协议`); // 埋点
    } else {
      window?.MyStat?.addPageEvent(
        "payment_unselect_terms",
        `取消选择支付协议`
      ); // 埋点
    }
    return;
  }
  window?.MyStat?.addPageEvent(
    "payment_open_terms",
    `打开支付协议对话框(未阅读完协议，点击勾选框，弹出协议对话框)`
  ); // 埋点
  orderPayAgreeRef?.value?.onOpenAgree();
}
</script>

<style scoped lang="scss">
.page-wrapper {
  width: 100%;
  color: #222;
  height: 100%;
  background-color: #f2f2f2;
  padding-bottom: 160px;
}
.page-content {
  width: 1280px;
  margin: 0 auto;
  padding-bottom: 60px;
}
table,
th,
td {
  border: 1px solid #d7d7d7;
  border-collapse: collapse;
}
tr th {
  padding: 8px 10px;
  line-height: 18px;
  background: #fff;
}
tr td {
  padding: 8px 10px;
  line-height: 18px;
}
thead th {
  color: #7f7f7f;
  background-color: #f2f2f2; /* 设置背景色为灰色 */
}
.transport-box {
  width: 480px;
  height: fit-content;
  border: 1px solid #d7d7d7;
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 0.25rem;
  box-shadow: 0 3px 4px -2px rgba(0, 0, 0, 0.18);
}

.page-footer {
  margin: 0 auto;
  width: 1280px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e2e2e2;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 80px;
  z-index: 9;
}
:deep(.n-radio--checked) {
  --n-text-color-disabled: rgb(51, 54, 57) !important;
  --n-dot-color-disabled: #e50113 !important;
  --n-box-shadow-disabled: inset 0 0 0 1px #e50113 !important;
}
.coupon-wrapper {
  :deep(.n-checkbox-box) {
    width: 20px;
    height: 20px;
  }
  :deep(.n-checkbox-box__border) {
    border-color: #333;
  }
}

:deep(.agree-checkbox.n-checkbox .n-checkbox-box) {
  background-color: #f2f2f2;
}

:deep(.agree-checkbox.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background-color: #e50113 !important;
}

:deep(.agree-checkbox .n-checkbox-box__border) {
  border: 1px solid #666;
}

.coupon-card {
  color: #333;
  .coupon-border {
    border-color: #333;
  }
  :deep(.n-checkbox__label) {
    color: #333;
  }
}

.checked-coupon {
  color: #e50113 !important;
  .coupon-border {
    border-color: #e50113;
  }
  :deep(.n-checkbox__label) {
    color: #e50113 !important;
  }
}

.disabled-coupon {
  color: #7f7f7f;
  .coupon-border {
    border-color: #7f7f7f;
  }
  :deep(.n-checkbox__label) {
    color: #7f7f7f;
  }
}

.loading-overlay {
  position: absolute;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
:deep(.n-checkbox__label) {
  width: calc(100% - 24px);
}
:deep(.n-checkbox-box-wrapper) {
  order: 2;
}
</style>
