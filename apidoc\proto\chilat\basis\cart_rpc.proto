syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";
import "chilat/basis/param/cart_param.proto";
import "chilat/basis/model/cart_model.proto";

// 购物车
service CartRpc {
    // 获取购物车统计信息
    rpc getCartStat (MidCartQueryParam) returns (MidCartStatResp);

    // 获取购物车信息（20250818-即将废弃）
    rpc getCart(MidCartQueryParam) returns (MidCartResp);

    // 获取购物车信息（按Tab分割）
    rpc getCartByTab(chilat.basis.MidCartQueryParam) returns (MidCartByTabResp);

    // 添加到购物车
    rpc addCart(MidCartAddParam) returns (MidCartByTabResp);

    // 修改购物车商品
    rpc updateCart(MidCartUpdateParam) returns (MidCartByTabResp);

    // 删除购物车商品
    rpc removeCart(MidCartRemoveParam) returns (MidCartByTabResp);

    // 将访客购物车，合并到登录用户中
    rpc mergeCart(MidMergeCartParam) returns (MidCartByTabResp);

    // 更新全部选择或全部不选中
    rpc updateSelectedAll(chilat.basis.MidUpdateSelectedAllParam) returns (chilat.basis.MidCartByTabResp);
}