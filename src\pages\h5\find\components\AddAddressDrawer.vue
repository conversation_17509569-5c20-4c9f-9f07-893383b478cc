<template>
  <n-drawer
    v-model:show="pageData.showAddAddressModal"
    placement="bottom"
    :height="'80%'"
    :trap-focus="false"
  >
    <n-drawer-content closable>
      <template #header>
        <div class="text-[0.36rem] font-medium">
          {{ authStore.i18n("cm_addr.selectShippingAddress") }}
        </div>
      </template>

      <div class="address-form pb-[68px]">
        <n-form
          ref="editFormRef"
          :model="editForm"
          :rules="rules"
          label-placement="top"
          require-mark-placement="right-hanging"
        >
          <n-form-item
            path="countryId"
            :label="authStore.i18n('cm_addr.country')"
          >
            <n-select
              filterable
              disabled
              value-field="id"
              label-field="countryEsName"
              :options="props.countryList"
              v-model:value="editForm.countryId"
              @update:value="onSelectCountry('update')"
              :placeholder="authStore.i18n('cm_addr.countryPh')"
              to="body"
            />
          </n-form-item>
          <!-- 联系人 -->
          <n-form-item
            path="contactName"
            :label="authStore.i18n('cm_addr.contact')"
          >
            <n-input
              v-trim
              clearable
              v-model:value="editForm.contactName"
              :placeholder="authStore.i18n('cm_addr.contactPh')"
            ></n-input>
          </n-form-item>
          <!-- WhatsApp -->
          <n-form-item path="phone" label="WhatsApp">
            <n-input-group>
              <n-input
                v-trim
                readonly
                class="!w-[70px]"
                @keydown.enter.prevent
                v-model:value="editForm.areaCode"
                :placeholder="authStore.i18n('cm_submit.telephonePrefix')"
              />
              <n-input
                v-trim
                clearable
                @keydown.enter.prevent
                v-model:value="editForm.phone"
                :placeholder="authStore.i18n('cm_search.pleaseInputWhatsapp')"
              />
            </n-input-group>
          </n-form-item>
          <!-- 州 -->
          <n-form-item
            path="province"
            :label="authStore.i18n('cm_addr.stateOrProvince')"
          >
            <n-select
              tag
              filterable
              value-field="name"
              label-field="name"
              children-field="child"
              :options="pageData.provinceList"
              v-model:value="editForm.province"
              @update:value="onSelectProvince('update')"
              :placeholder="authStore.i18n('cm_addr.stateOrProvincePh')"
              to="body"
            >
            </n-select>
          </n-form-item>
          <n-form-item path="city" :label="authStore.i18n('cm_addr.city')">
            <n-select
              tag
              filterable
              value-field="name"
              label-field="name"
              :options="pageData.cityList"
              v-model:value="editForm.city"
              :placeholder="authStore.i18n('cm_addr.cityPh')"
              to="body"
            >
            </n-select>
          </n-form-item>
          <!-- 邮政编码 -->
          <n-form-item
            path="postcode"
            :label="authStore.i18n('cm_addr.postcode')"
          >
            <n-input
              v-trim
              clearable
              v-model:value="editForm.postcode"
              :input-props="{ type: 'number' }"
              :placeholder="authStore.i18n('cm_addr.postcodePh')"
            ></n-input>
          </n-form-item>

          <n-form-item
            :label="authStore.i18n('cm_addr.address')"
            path="address"
          >
            <n-input
              v-trim
              clearable
              style="width: 100%"
              v-model:value="editForm.address"
              :placeholder="authStore.i18n('cm_addr.addressPh')"
            ></n-input>
          </n-form-item>
          <n-checkbox v-model:checked="editForm.isDefault">
            <span class="ml-[6px]">
              {{ authStore.i18n("cm_addr.defaultFlag") }}
            </span>
          </n-checkbox>
        </n-form>

        <div
          class="fixed bottom-0 left-0 right-0 bg-white border-t border-[#F5F5F5] p-[0.16rem] bg-white z-10"
          v-if="pageData.type === 'modal'"
        >
          <div class="flex gap-[0.16rem]">
            <n-button
              size="large"
              class="flex-1 h-[0.88rem]"
              @click="onCloseAddAddrForm"
            >
              {{ authStore.i18n("cm_addr.cancelBtn") }}
            </n-button>
            <n-button
              size="large"
              color="#E50113"
              text-color="#fff"
              class="flex-1 h-[0.88rem]"
              @click="onSaveAndUseAddress('modal')"
            >
              {{ authStore.i18n("cm_addr.confirmBtn") }}
            </n-button>
          </div>
        </div>

        <!-- inline 模式按钮 -->
        <div
          v-else
          class="fixed bottom-0 left-0 right-0 bg-white border-t border-[#F5F5F5] p-[0.16rem] bg-white z-10"
        >
          <n-button
            block
            round
            size="large"
            color="#E50113"
            text-color="#fff"
            class="w-full h-[0.88rem]"
            @click="onSaveAndUseAddress('inline')"
          >
            {{ authStore.i18n("cm_addr.saveAndUseAddress") }}
          </n-button>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import type { FormRules, FormItemRule } from "naive-ui";
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const props = defineProps({
  countryList: {
    type: Array,
    default: () => [],
  },
  countryRegexes: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["onUpdateListUserAddress"]);
const editFormRef = ref();
const editForm = reactive(<any>{
  contactName: "",
  phone: "",
  areaCode: "",
  countryId: "",
  province: "",
  city: "",
  postcode: "",
  address: "",
  isDefault: false,
});

const pageData = reactive<any>({
  type: "modal",
  provinceList: <any>[],
  cityList: <any>[],
  showAddAddressModal: false,
});

// 计算属性：是否为编辑模式
const isEdit = computed(() => Boolean(editForm.id));

const rules: FormRules = {
  contactName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.contactPh"),
  },
  phone: {
    required: true,
    trigger: "blur",
    message: (() => {
      const phoneCountMessage = props.countryRegexes?.phoneCount
        ? `${authStore.i18n("cm_submit.whatsappTips")} ${
            props.countryRegexes?.phoneCount
          } ${authStore.i18n("cm_submit.whatsapp")}`
        : authStore.i18n("cm_submit.whatsappRequired");
      return `${phoneCountMessage}`;
    })(),
    validator(rule: FormItemRule, value: any) {
      console.log("props.countryRegexes :>> ", props.countryRegexes);
      const lengths =
        props.countryRegexes.phoneCount &&
        props.countryRegexes.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.countryPh"),
  },
  postcode: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.postcodePh"),
  },
  province: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.provincePh"),
  },
  city: {
    required: true,
    trigger: "change",
    message: authStore.i18n("cm_addr.cityPh"),
  },
  address: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.addressPh"),
  },
};

watch(
  () => props.countryRegexes,
  (val) => {
    if (val?.phoneCount) {
      (rules["phone"] as any).message = `${authStore.i18n(
        "cm_submit.whatsappTips"
      )} ${val?.phoneCount} ${authStore.i18n("cm_submit.whatsapp")}`;
    }
  },
  { deep: true }
);

function onShowDrawer(type?: any, address?: any) {
  Object.keys(editForm).forEach((key) => {
    delete editForm[key];
  });
  // 确保国家ID是秘鲁
  editForm.countryId = props.countryRegexes.id;
  editForm.areaCode = props.countryRegexes.areaCode;

  // 清空省市数据
  pageData.provinceList = [];
  pageData.cityList = [];

  if (address) {
    Object.assign(editForm, address);
  }

  if (editForm.countryId) {
    onSelectCountry();
    if (pageData.provinceList && editForm.province) {
      onSelectProvince();
    }
  }
  pageData.type = type;
  pageData.showAddAddressModal = true;
}

function onSelectProvince(type?: any) {
  const matchProvince = pageData.provinceList.find(
    (item: any) => item.name === editForm.province
  );
  pageData.cityList = matchProvince?.children;
  if (type === "update") {
    editForm.city = "";
  }
}

async function onSelectCountry(type?: any) {
  const res: any = await useListRegionByCountry({ id: editForm.countryId });
  if (res?.result?.code === 200) {
    onHandleRegion(res?.data, type);
  } else {
    onHandleRegion([], type);
    showToast(
      res.result?.message || authStore.i18n("cm_addr.loadRegionFailed")
    );
  }
}

function onHandleRegion(data: any, type?: any) {
  pageData.provinceList = data;
  pageData.cityList = [];
  if (type === "update") {
    editForm.province = "";
    editForm.city = "";
  }
}

function onCloseAddAddrForm() {
  pageData.showAddAddressModal = false;
}

async function onSaveAndUseAddress(type: any) {
  try {
    await editFormRef.value?.validate();
    const matchProvince = pageData.provinceList.find(
      (item: any) => item.name === editForm.province
    );
    const matchCity = matchProvince?.children?.find(
      (item: any) => item.name === editForm.city
    );

    const provinceCode = matchProvince?.code;
    const cityCode = matchCity?.code;
    let params = {
      ...editForm,
      cityCode,
      provinceCode,
    };
    const res: any = await useSaveUserAddress(params);
    if (res?.result?.code === 200) {
      // 关闭抽屉
      pageData.showAddAddressModal = false;

      // 通知父组件更新地址列表
      emit("onUpdateListUserAddress", type);

      // 显示成功消息
      const isEdit = Boolean(editForm.id);
      const successMessage = isEdit
        ? authStore.i18n("cm_addr.editSuccess")
        : authStore.i18n("cm_addr.addSuccess");
      showToast(successMessage);
    } else {
      showToast(res.result?.message);
    }
  } catch (error) {
    console.error("保存地址失败:", error);
  }
}

defineExpose({
  onShowDrawer,
});
</script>

<style scoped lang="scss"></style>
