syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";

message MidCartQueryParam {
    string token = 1; //前端无需传该值
    string goodsId = 2; //商品id
    int32 siteId = 3; //站点ID
    string padc = 88; //促销活动动态代码
}

message MidCartAddParam {
    string token = 10; //前端无需传该值
    string goodsId = 20; // 商品ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
    string routeId = 40; // 线路ID
    repeated MidCartSkuAddParam skus = 50; //sku列表
    string padc = 88; //促销活动动态代码
}

message MidCartSkuAddParam {
    string skuId = 1; // 商品SKU ID
    int32 quantity = 2; // 数量
    string spm = 3; //SPM跟踪码（在商详页中的SKU列表中添加到购物车，从location.href中取spm参数，参考代码：window.MyStat.getLocationParam('spm')）
    string padc = 88; //促销活动动态代码
}

message MidCartUpdateParam {
    string token = 1; //前端无需传该值
    string skuId = 2; // 商品SKU ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
    int32 quantity = 3; // 数量
    bool selected = 4; // 是否选中
    string updatedSkuId = 5; // 更新后的商品SKU ID
    string goodsId = 6; // 商品ID
    bool selectedAll = 7; // 全部选中（20250818-即将废弃，请使用 updateSelectedAll 接口）
    string padc = 88; //促销活动动态代码
}

message MidCartRemoveParam {
    string token = 1; //前端无需传该值
    string skuId = 2; // 商品SKU ID
    string goodsId = 3; // 商品ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
    string padc = 88; //促销活动动态代码
}

//购物车合并参数
message MidMergeCartParam {
    string visitorId = 10; //访客ID
    string userId = 20; //商城用户ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
}

//更新全部选择或全部不选中的参数
message MidUpdateSelectedAllParam {
    string token = 10; //前端无需传该值
    int32 siteId = 20; //站点ID
    bool supportOnlineOrder = 30; //支持在线订单（必填，再“在线下单”TAB中的商品，传true）
    bool selected = 40; //是否选择（必填，true表示选中，false表示不选中）
}