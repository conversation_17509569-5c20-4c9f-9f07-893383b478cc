syntax = "proto3";
package mall.passport;

option java_package = "com.chilat.rpc.passport.model";

import "common.proto";
import "common/business.proto";
import "chilat/coupon/coupon_common.proto";


message UserDetailResp {
  common.Result result = 1;
  UserModel data = 2;
}

message UserModel {
  string id = 1;
  string realName = 2; //姓名
  string email = 3; //邮箱
  string countryId = 4;
  string countryName = 5; //国家
  string whatsapp = 6;
  string inviteCode = 7; //邀请码
  int32 inquiryCount = 8; //询盘总数量
}

message UserAddressResp {
  common.Result result = 1;
  repeated UserAddressModel data = 2;
}

message UserAddressModel {
  string id = 1;
  string countryId = 2; //国家
  string countryName = 3; //国家名称
  string provinceCode = 4; //省
  string province = 5; //省
  string cityCode = 6; //市
  string city = 7; //市
  string regionCode = 8; //区
  string region = 9; //区
  string address = 10; //详细地址
  string houseNo = 11; //门牌号
  string postcode = 12; //邮编
  string contactName = 13; //联系人
  string areaCode = 30; //区号
  string phone = 14; //手机号
  bool isDefault = 15; //是否是默认地址
  string referLandmark = 16; //参考地标
  string fullAddress = 17; //完整地址
  common.AddressLabel addressLabel = 18; //地址标签
  string street = 19;
}

message SendVerifyMailResp {
  common.Result result = 1;
  SendVerifyMailRespModel data = 2;
}

message SendVerifyMailRespModel {
  bool isMailVerified = 1;
}

message QueryVerifyMailResultResp {
  common.Result result = 1;
  QueryVerifyMailResultModel data = 2;
}

message QueryVerifyMailResultModel {
  bool isMailVerified = 10; //是否邮箱已激活
  repeated CouponModel couponList= 20; //优惠券列表
  int32 expireHour = 30; //验证邮件里的链接有效期
}

message CouponModel {
  string couponId = 10; //券ID
  string couponName = 20; //券名称
  int32 count = 30; //数量
  chilat.coupon.CouponTypeStatus couponType = 40; //券类型 couponTypeProduct 产品券 couponTypeCommission 佣金券
  chilat.coupon.CouponWayStatus couponWay = 50; //优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券
  double preferentialAmount = 60; //优惠额度
  double discount = 70; //折扣（券类型为折扣券使用）
}

message VerifyMailResp {
  common.Result result = 1;
  VerifyMailModel data = 2;
}

message VerifyMailModel {
  common.VerifyMailResultEnum verifyResult = 1;
  common.VerifyMailSceneEnum scene = 2;
}

message InvitedUserMailStatusResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated UserBasicModel data = 3; //用户列表
}

message UserBasicModel {
  string email = 10; //邮箱
  int64 registerTime = 20; //注册时间, 毫秒级时间戳
}