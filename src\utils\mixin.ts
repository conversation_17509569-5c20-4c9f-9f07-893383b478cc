import _ from "lodash";

export const currencyUnit = "US";
export const monetaryUnit = "US$";

/** 设置货币单位 */
export function setUnit(val: number): string {
  if (isNaN(val) || val === null || val === undefined) return "";

  // 处理负数的情况
  const isNegative = val < 0;
  const absoluteValue = Math.abs(val);

  // 使用 toLocaleString() 格式化数字，添加千位分隔符和两位小数
  const formattedValue = absoluteValue.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  return (isNegative ? "- " : "") + monetaryUnit + " " + formattedValue;
}

// 格式化金额
export function setNewUnit(val: number, noUnit?: any): string {
  if (isNaN(val) || val === null || val === undefined) return "";

  // 取绝对值的逻辑去掉，仅保留格式化逻辑
  const hasDecimal = val % 1 !== 0;

  // 使用 toLocaleString() 格式化数字，根据是否有小数决定展示两位小数或整数
  const formattedValue = val.toLocaleString("en-US", {
    minimumFractionDigits: hasDecimal ? 2 : 0,
    maximumFractionDigits: hasDecimal ? 2 : 0,
  });
  if (noUnit) {
    return formattedValue;
  }
  return monetaryUnit + " " + formattedValue;
}

// 浮点数四舍五入
export function mathRound(val: any, precision: number = 2) {
  if (isNaN(val)) return 0;

  if (precision <= 0) {
    return parseInt(val) ?? 0;
  }

  const num = Number(val);
  return (
    Math.round((num + Number.EPSILON) * Math.pow(10, precision)) /
    Math.pow(10, precision)
  );
}

// 小数转换为百分比
export function decimalToPercentage(decimal: any, decimals: number = 0) {
  return (decimal * 100).toFixed(decimals) + "%";
}

// 折扣小数转换为百分比数值（1 - discount）
export function discountToPercentage(
  discount: number,
  decimals: number = 0
): string {
  return ((1 - discount) * 100).toFixed(decimals) + "%";
}

/** 构建带参数的URL */
export function buildQueryString(path: string, params: any) {
  const param = Object.keys(params)
    .map(
      (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
    )
    .join("&");
  return param ? `${path}?${param}` : path;
}

/** 数组比较 */
export function arrayCompare(select: any, specItems: any, by: any) {
  const sortedArray1 = _.sortBy(select, by);
  const sortedArray2 = _.sortBy(specItems, by);
  return _.isEqual(sortedArray1, sortedArray2);
}

/** 二维对象数组的笛卡尔乘积 */
export function cartesianProduct(arrays: any[]) {
  if (!Array.isArray(arrays) || arrays.length <= 0) {
    return [];
  }

  const result: any[] = [];
  const product = (arr: any, index: any, current: any) => {
    if (index === arrays.length) {
      result.push({ ...current });
      return;
    }
    for (const item of arr[index]) {
      current = { ...current, ...item };
      product(arr, index + 1, current);
      // 恢复 current 的初始状态以便处理下一个元素
      for (const key in item) {
        delete current[key];
      }
    }
  };
  product(arrays, 0, {});
  return result;
}

/** 数组去重 */
export function recursiveQuery(obj: any, fieldName: any, value: any): any {
  // Check if the current object has the field and its value matches the target value
  if (_.has(obj, fieldName) && obj[fieldName] === value) {
    return true;
  }

  // Iterate through all properties of the object
  return _.some(obj, (val: any) => {
    // If the property is an object or array, recursively check its properties
    if (_.isObject(val) || _.isArray(val)) {
      return recursiveQuery(val, fieldName, value);
    }
  });
}

// 判断是否是空对象
export function isEmptyObject(obj: any) {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
}

// 下载文件
export async function downloadFile(blob: Blob, filename: string) {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 复制 navigator.clipboard只在安全上下文（HTTPS）中可用
export async function onCopyText(text: any) {
  if (!navigator.clipboard) {
    onFallbackCopyText(text);
  } else {
    try {
      await navigator.clipboard.writeText(text);
      showToast("Copia exitosa", 1500);
    } catch (err) {
      // showToast("La copia falló, por favor vuelva a intentarlo más tarde");
      onFallbackCopyText(text);
    }
  }
}

// 复制 document.execCommand 用来兼容不支持Clipboard API的浏览器
export async function onFallbackCopyText(text: any) {
  const textArea = document.createElement("textarea");
  textArea.value = text;

  // 确保 textarea 不会触发键盘
  textArea.setAttribute("readonly", "true");
  textArea.style.position = "absolute";
  textArea.style.opacity = "0";
  textArea.style.pointerEvents = "none";
  textArea.style.zIndex = "-1";

  document.body.appendChild(textArea);
  textArea.select();

  try {
    const successful = document.execCommand("copy");
    if (successful) {
      showToast("Copia exitosa", 1500);
    } else {
      showToast("La copia falló, por favor vuelva a intentarlo más tarde");
    }
  } catch (err) {
    showToast("La copia falló, por favor vuelva a intentarlo más tarde");
  }

  document.body.removeChild(textArea);
}

export function navigateUrl(
  path: string,
  query: Record<string, any>,
  event?: Event
): string {
  let url = path;

  if (event) {
    query.spm = window.MyStat.getPageSPM(event);
  }

  if (query && Object.keys(query).length > 0) {
    const queryString = Object.keys(query)
      .map(
        (key) => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`
      )
      .join("&");

    // 判断 path 是否已经包含 ?
    url += path.includes("?") ? `&${queryString}` : `?${queryString}`;
  }

  return url;
}

// 页面跳转
export function navigateToPage(
  path: string, //路径
  query: Record<string, any>, //查询参数
  openNewTab: boolean, //是否在新窗口打开
  event?: Event // 事件对象 如果传递了事件对象 默认在链接拼接spm参数
) {
  const url = navigateUrl(path, query, event);
  if (openNewTab) {
    window.open(url, "_blank");
  } else {
    window.location.href = url;
  }
}

// 根据邮箱地址跳转到邮箱页面
export function navigateToEmail(email?: any) {
  if (!email) {
    // 默认取当前用户邮箱
    const config = useRuntimeConfig();
    const userInfo = ref<any>({});
    userInfo.value = config.public.userInfo as object;
    email = userInfo?.value.username;
  }
  // 常见邮箱页面映射
  const emailLoginPages = <any>{
    "gmail.com": "https://gmail.com",
    "hotmail.com": "https://outlook.com",
    "outlook.com": "https://outlook.com",
    "yahoo.com": "https://mail.yahoo.com",
    "icloud.com": "https://icloud.com/mail",
    "qq.com": "https://wx.mail.qq.com",
    "yahoo.com.mx": "https://mail.yahoo.com",
    "live.com.mx": "https://outlook.com",
    "outlook.es": "https://outlook.com",
    "yahoo.es": "https://mail.yahoo.com",
    "hotmail.es": "https://outlook.com",
    "msn.com": "https://outlook.com",
    "gmail.com.pe": "https://gmail.com",
    "aol.com": "https://mail.aol.com",
    "live.com": "https://outlook.com",
    "mail.com": "https://mail.com",
    "yahoo.com.ar": "https://mail.yahoo.com",
    "rocketmail.com": "https://mail.yahoo.com",
    "yahoo.com.pe": "https://mail.yahoo.com",
    "gmail.com.mx": "https://gmail.com",
    "googlemail.com": "https://gmail.com",
    "hmail.com": "https://hmail.com",
    "ymail.com": "https://mail.yahoo.com",
    "prodigy.net.mx": "https://telmex.com/infinitummail",
    "126.com": "https://126.com",
  };

  // 提取邮箱域名
  const domain = email.split("@")[1];
  if (!domain) {
    window?.MyStat?.addPageEvent(
      "passport_jump_mail_error",
      `跳转邮箱登录链接错误：correo electrónico no válida`
    ); // 埋点
    showToast("correo electrónico no válida");
    return;
  }

  // 跳转到对应的邮箱页面
  const emailPage = emailLoginPages[domain];
  if (emailPage) {
    window?.MyStat?.addPageEvent(
      "click_outbound_link",
      `点击链接：${emailPage}`
    ); // 埋点
    window.open(emailPage, "_blank");
  } else {
    window?.MyStat?.addPageEvent(
      "passport_jump_mail_error",
      `跳转邮箱登录链接错误：Por favor, vaya a su correo electrónico para verificarlo`
    ); // 埋点
    showToast("Por favor, vaya a su correo electrónico para verificarlo");
  }
}

// 移除末尾斜杠
export function normalizePath(path: any) {
  return path === "/" ? path : path.replace(/\/+$/, "");
}

function generateApiPath(routePath: string): string {
  if (
    /^\/tienda\/[^/]+$/.test(routePath) ||
    /^\/h5\/tienda\/[^/]+$/.test(routePath)
  ) {
    return "tienda-detail";
  }

  if (routePath === "/") {
    return "/";
  }

  if (/^(\/h5\/?)$/.test(routePath)) {
    return "h5";
  }

  let normalizedPath = routePath.replace(/^\//, "");
  if (normalizedPath.startsWith("h5/")) {
    normalizedPath = normalizedPath.substring(3);
  }

  return normalizedPath;
}

function createDefaultWhatsAppConfig() {
  const phone = "8613385799309";
  const text = "quiero saber sobre importar desde China, ¿puedes ayudarme?";
  return {
    jumpUrl: `whatsapp://send/?phone=${phone}&text=${encodeURIComponent(
      text
    )}&type=phone_number&app_absent=0`,
  };
}

export async function loadWhatsAppConfig() {
  let whatsAppConfig = null;
  try {
    const route = useRoute();
    const apiPath = generateApiPath(route.path);
    const { useGetPageWhatsAppConfig } = await import("@/composables/http");

    const res: any = await useGetPageWhatsAppConfig({
      pageCode: apiPath,
      visitSite: "VISIT_SITE_SELECT_SHOP",
      useDefaultConfig: true,
    });

    if (res?.result?.code === 200 && res?.data) {
      whatsAppConfig = {
        jumpUrl: res.data.jumpUrl,
      };
    } else {
      whatsAppConfig = createDefaultWhatsAppConfig();
    }
  } catch (error) {
    whatsAppConfig = createDefaultWhatsAppConfig();
  }
  return whatsAppConfig;
}

// WhatsApp点击追踪状态管理
interface WhatsAppClickState {
  clickCount: number; // 有效点击计数（间隔1秒以上才+1）
  lastClickTime: number; // 上次点击时间
  pageLoadTime: number; // 页面加载时间
}

// 全局状态存储
let whatsAppClickState: WhatsAppClickState = {
  clickCount: 0,
  lastClickTime: 0,
  pageLoadTime: Date.now(),
};

// 重置点击状态（页面刷新时调用）
export function resetWhatsAppClickState() {
  whatsAppClickState = {
    clickCount: 0,
    lastClickTime: 0,
    pageLoadTime: Date.now(),
  };
}

export async function onWhatsAppClick() {
  const whatsAppConfig = await loadWhatsAppConfig();
  const jumpUrl = whatsAppConfig.jumpUrl;
  const currentTime = Date.now();

  // 间隔1秒以上才算下一次点击
  const isNewClick =
    whatsAppClickState.clickCount === 0 ||
    currentTime - whatsAppClickState.lastClickTime >= 1000;

  if (isNewClick) {
    whatsAppClickState.clickCount++;
    // 更新最后点击时间
    whatsAppClickState.lastClickTime = currentTime;
  }

  let eventName: string;
  let eventDescription: string;

  switch (whatsAppClickState.clickCount) {
    case 1:
      eventName = "click_whatsapp";
      eventDescription = `点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
    case 2:
      eventName = "click_whatsapp_second";
      eventDescription = `二次点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
    case 3:
      eventName = "click_whatsapp_third";
      eventDescription = `三次点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
    default:
      eventName = "click_whatsapp_more";
      eventDescription = `第${whatsAppClickState.clickCount}次点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
  }

  (window as any)?.MyStat?.addPageEvent(eventName, eventDescription, () => {
    window.location.href = jumpUrl;
  });
}
