syntax = "proto3";
package chilat.basis;


option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/checkout_model.proto";
import "chilat/basis/param/checkout_param.proto";
import "common.proto";
import "mall/main/param/mall_config_param.proto";

// 订单结算服务
service CheckoutRpc {
    // 解析购买的SKU文本
    rpc parseBuySkuText(ParseBuySkuTextParam) returns (ParseBuySkuTextResp);

    // 预览购买的SKU列表
    rpc previewBuySkuList(PreviewBuySkuListParam) returns (PreviewBuySkuListResp);

    // 预估运费
    rpc calculateEstimateFreight(MidEstimateFreightParam) returns (MidEstimateFreightResp);

    // 快速下单接口
    rpc fastCreateOrder(MidFastCreateOrderParam) returns (MidFastCreateOrderResp);

}